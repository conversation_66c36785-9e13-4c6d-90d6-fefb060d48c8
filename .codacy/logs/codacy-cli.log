2025-09-10T10:52:15-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T10:52:15-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=init full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 init] args=[]
2025-09-10T10:52:17-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T10:52:17-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py] command=analyze
2025-09-10T10:52:17-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pmd toolConfigPath=.codacy/tools-configs/ruleset.xml
2025-09-10T10:52:17-04:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:32) PMD binary path pmdBinary=/Users/<USER>/.cache/codacy/tools/pmd@7.11.0/pmd-bin-7.11.0/bin/pmd
2025-09-10T10:52:17-04:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:98) Setting up Java environment javaHome=/Users/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7
2025-09-10T10:52:17-04:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:144) Updated environment variables javaHome=JAVA_HOME=/Users/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7 path=PATH=/Users/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/Contents/Home/bin:/Users/<USER>/Library/Python/3.9/bin:/usr/bin:/Users/<USER>/.nvm/versions/node/v23.9.0/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.local/bin:/Library/Frameworks/Python.framework/Versions/3.11/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Applications/Privileges.app/Contents/MacOS binDir=/Users/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/Contents/Home/bin javaBinary=/Users/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/Contents/Home/bin/java
2025-09-10T10:52:17-04:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:160) Running PMD command command=/Users/<USER>/.cache/codacy/tools/pmd@7.11.0/pmd-bin-7.11.0/bin/pmd check --no-fail-on-violation -R .codacy/tools-configs/ruleset.xml -d /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py -f sarif -r /var/folders/36/9g20464s5ld82n59_3130xf00000gp/T/codacy-analysis-2195976693/pmd.sarif
2025-09-10T10:52:19-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T10:52:19-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=revive toolConfigPath=.codacy/tools-configs/revive.toml
2025-09-10T10:52:19-04:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/Users/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py
2025-09-10T10:52:22-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T10:52:22-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=install full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 install] args=[]
2025-09-10T10:52:22-04:00 [INFO] (codacy-cli-v2/cmd/install.go:72) All components are already installed
2025-09-10T10:52:22-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T10:52:22-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=discover full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 config discover /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T10:52:23-04:00 [DEBUG] (codacy-cli-v2/cmd/config.go:181) Detected recognizable file extensions extensions=[.py (1 files)] path=/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py
2025-09-10T10:52:23-04:00 [DEBUG] (codacy-cli-v2/config/detector.go:109) Found relevant tool for extension tool=trivy extension=.py count=1
2025-09-10T10:52:23-04:00 [DEBUG] (codacy-cli-v2/config/detector.go:109) Found relevant tool for extension count=1 tool=semgrep extension=.py
2025-09-10T10:52:23-04:00 [DEBUG] (codacy-cli-v2/config/detector.go:109) Found relevant tool for extension tool=pylint extension=.py count=1
2025-09-10T10:52:23-04:00 [DEBUG] (codacy-cli-v2/config/detector.go:109) Found relevant tool for extension tool=lizard extension=.py count=1
2025-09-10T10:52:23-04:00 [DEBUG] (codacy-cli-v2/config/detector.go:121) Detected relevant tools for path tools=[lizard pylint semgrep trivy] path=/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py
2025-09-10T10:52:25-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
2025-09-10T10:52:25-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/analysis_options.yaml tool=dartanalyzer
2025-09-10T10:52:26-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=eslint toolConfigPath=.codacy/tools-configs/eslint.config.mjs
2025-09-10T11:02:31-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T11:02:31-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/Rasterize.py] command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/Rasterize.py --format sarif]
2025-09-10T11:02:31-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T11:02:31-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T11:02:31-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T11:02:34-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:42:04-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:42:04-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:42:04-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:42:09-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:42:09-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:42:09-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:42:26-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:42:26-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py] command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif]
2025-09-10T13:42:26-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
2025-09-10T13:42:26-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/lizard.yaml tool=lizard
2025-09-10T13:42:26-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:42:26-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:42:50-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:42:50-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py] command=analyze
2025-09-10T13:42:50-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:42:50-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:42:50-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:42:54-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:43:21-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:43:21-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py] command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif]
2025-09-10T13:43:21-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:43:21-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/pylint.rc tool=pylint
2025-09-10T13:43:22-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:43:25-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:43:39-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:43:39-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:43:39-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:43:43-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:43:43-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:43:43-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:43:48-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:43:48-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py] command=analyze
2025-09-10T13:43:48-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:43:48-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/pylint.rc tool=pylint
2025-09-10T13:43:48-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/semgrep.yaml tool=semgrep
2025-09-10T13:43:52-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:43:56-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:43:56-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:43:56-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:43:56-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:43:57-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:44:00-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:44:03-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:44:03-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:44:03-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:44:06-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:44:06-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:44:06-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:45:27-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:45:27-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py] command=analyze
2025-09-10T13:45:27-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/lizard.yaml tool=lizard
2025-09-10T13:45:27-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/pylint.rc tool=pylint
2025-09-10T13:45:28-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/semgrep.yaml tool=semgrep
2025-09-10T13:45:31-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
2025-09-10T13:45:34-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:45:34-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py] command=analyze
2025-09-10T13:45:34-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:45:34-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:45:38-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:45:38-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:45:40-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:45:40-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py] command=analyze
2025-09-10T13:45:40-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/semgrep.yaml tool=semgrep
2025-09-10T13:45:44-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:45:44-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:45:44-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:45:47-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:45:47-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:45:47-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:45:50-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:45:50-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:45:50-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:46:18-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:46:18-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:46:18-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/lizard.yaml tool=lizard
2025-09-10T13:46:18-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:46:19-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:46:22-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:46:27-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:46:27-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:46:27-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:46:30-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
2025-09-10T13:46:30-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:46:30-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:46:57-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:46:57-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py] command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif]
2025-09-10T13:46:57-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:46:57-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/pylint.rc tool=pylint
2025-09-10T13:46:58-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:47:01-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:47:29-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:47:29-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py] command=analyze
2025-09-10T13:47:29-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:47:29-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:47:33-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:47:33-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:48:42-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:48:42-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:48:42-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:48:42-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/pylint.rc tool=pylint
2025-09-10T13:48:43-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:48:46-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:49:23-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:49:23-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:49:23-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/semgrep.yaml tool=semgrep
2025-09-10T13:49:26-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:49:26-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:49:26-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/pylint.rc tool=pylint
2025-09-10T13:49:53-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:49:53-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py] command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif]
2025-09-10T13:49:53-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:49:53-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/pylint.rc tool=pylint
2025-09-10T13:49:53-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:49:57-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
2025-09-10T13:50:00-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:50:00-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:50:00-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:50:00-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:50:00-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/semgrep.yaml tool=semgrep
2025-09-10T13:50:04-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:50:20-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:50:20-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:50:20-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:50:24-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
2025-09-10T13:50:24-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:50:24-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/pylint.rc tool=pylint
2025-09-10T13:50:43-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:50:43-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:50:43-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:50:43-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:50:44-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:50:47-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:51:52-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:51:52-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py] command=analyze
2025-09-10T13:51:52-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:51:52-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/pylint.rc tool=pylint
2025-09-10T13:51:53-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/semgrep.yaml tool=semgrep
2025-09-10T13:51:57-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
2025-09-10T13:52:31-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:52:31-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:52:31-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:52:31-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:52:31-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:52:35-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:52:45-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:52:45-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/debug_geom.txt] command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/debug_geom.txt --format sarif]
2025-09-10T13:53:00-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:53:00-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:53:00-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:53:03-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
2025-09-10T13:53:03-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/lizard.yaml tool=lizard
2025-09-10T13:53:04-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:53:09-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:53:09-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:53:09-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:53:09-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:53:09-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:53:10-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/semgrep.yaml tool=semgrep
2025-09-10T13:53:42-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:53:42-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:53:42-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:53:43-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:53:43-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:53:47-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:54:24-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:54:24-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py] command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif]
2025-09-10T13:54:24-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:54:24-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:54:24-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:54:28-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
2025-09-10T13:54:32-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:54:32-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py] command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif]
2025-09-10T13:54:32-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:54:33-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:54:36-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T13:54:36-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T13:56:08-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:56:08-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py] command=analyze
2025-09-10T13:56:08-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/lizard.yaml tool=lizard
2025-09-10T13:56:08-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:56:08-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/semgrep.yaml tool=semgrep
2025-09-10T13:56:12-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
2025-09-10T13:57:40-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:57:40-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T13:57:40-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/lizard.yaml tool=lizard
2025-09-10T13:57:40-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T13:57:40-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T13:57:44-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
2025-09-10T13:58:30-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T13:58:30-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/outputs/processing_report.txt] command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/outputs/processing_report.txt --format sarif]
2025-09-10T14:04:34-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T14:04:34-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/compute_flowlines.py]
2025-09-10T14:04:34-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T14:04:34-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T14:04:35-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T14:04:35-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T15:47:57-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T15:47:57-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/regenerate_stream_network.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/regenerate_stream_network.py]
2025-09-10T15:47:57-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T15:48:02-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T15:48:02-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T15:48:02-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T17:06:50-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:06:50-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py] command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py --format sarif]
2025-09-10T17:06:50-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T17:06:51-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T17:06:51-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/pylint.rc tool=pylint
2025-09-10T17:06:51-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T17:09:00-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:09:00-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py] command=analyze
2025-09-10T17:09:00-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T17:09:03-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
2025-09-10T17:09:03-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T17:09:03-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T17:09:12-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:09:12-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py] command=analyze
2025-09-10T17:09:12-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T17:09:15-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T17:09:15-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/lizard.yaml tool=lizard
2025-09-10T17:09:15-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T17:09:22-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:09:22-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py] command=analyze
2025-09-10T17:09:22-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/lizard.yaml tool=lizard
2025-09-10T17:09:22-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/pylint.rc tool=pylint
2025-09-10T17:09:22-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T17:09:26-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
2025-09-10T17:09:32-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:09:32-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py]
2025-09-10T17:09:32-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/pylint.rc tool=pylint
2025-09-10T17:09:32-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/semgrep.yaml tool=semgrep
2025-09-10T17:09:35-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T17:09:35-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T17:10:09-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:10:09-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py]
2025-09-10T17:10:09-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T17:10:09-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/semgrep.yaml tool=semgrep
2025-09-10T17:10:13-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T17:10:13-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T17:10:19-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:10:19-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py]
2025-09-10T17:10:19-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
2025-09-10T17:10:19-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T17:10:19-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/pylint.rc tool=pylint
2025-09-10T17:10:20-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T17:10:29-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:10:29-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py]
2025-09-10T17:10:29-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T17:10:33-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T17:10:33-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/lizard.yaml tool=lizard
2025-09-10T17:10:33-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T17:10:40-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:10:40-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py] command=analyze
2025-09-10T17:10:40-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/pylint.rc tool=pylint
2025-09-10T17:10:40-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T17:10:44-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
2025-09-10T17:10:44-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/lizard.yaml tool=lizard
2025-09-10T17:10:50-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:10:50-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_flowlines.py]
2025-09-10T17:10:50-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T17:10:50-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T17:10:50-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T17:10:50-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T17:11:40-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:11:40-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_constrained_streamnet.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_constrained_streamnet.py] command=analyze
2025-09-10T17:11:40-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/pylint.rc tool=pylint
2025-09-10T17:11:41-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T17:11:44-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T17:11:45-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T17:12:08-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:12:08-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_constrained_streamnet.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_constrained_streamnet.py]
2025-09-10T17:12:08-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T17:12:08-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T17:12:12-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T17:12:12-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T17:12:19-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:12:19-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_constrained_streamnet.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_constrained_streamnet.py]
2025-09-10T17:12:19-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T17:12:23-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T17:12:23-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T17:12:23-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T17:13:41-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:13:41-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_constrained_streamnet.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_constrained_streamnet.py]
2025-09-10T17:13:41-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T17:13:41-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T17:13:41-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T17:13:44-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T17:13:56-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:13:56-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_constrained_streamnet.py] command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_constrained_streamnet.py --format sarif]
2025-09-10T17:13:56-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/lizard.yaml tool=lizard
2025-09-10T17:13:56-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T17:13:56-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T17:13:59-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T17:15:13-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:15:13-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_streamnet.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_downstream_streamnet.py]
2025-09-10T17:15:13-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T17:15:14-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/semgrep.yaml tool=semgrep
2025-09-10T17:15:18-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T17:15:18-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T17:19:06-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T17:19:06-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/outputs/downstream_network_report.md --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/outputs/downstream_network_report.md]
2025-09-10T18:36:43-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T18:36:43-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/rebuild_downstream_network.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/rebuild_downstream_network.py]
2025-09-10T18:36:43-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T18:36:43-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T18:36:43-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T18:36:47-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T18:37:36-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T18:37:36-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/outputs/expanded_downstream_network_report.md --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/outputs/expanded_downstream_network_report.md]
2025-09-10T18:39:05-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T18:39:05-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_expanded_streamnet.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_expanded_streamnet.py]
2025-09-10T18:39:05-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T18:39:06-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T18:39:09-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=trivy toolConfigPath=.codacy/tools-configs/trivy.yaml
2025-09-10T18:39:10-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/lizard.yaml tool=lizard
2025-09-10T18:43:12-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T18:43:12-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/outputs/comprehensive_network_report.md --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/outputs/comprehensive_network_report.md]
2025-09-10T22:47:49-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T22:47:49-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_extended_downstream_path.py --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_extended_downstream_path.py]
2025-09-10T22:47:49-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
2025-09-10T22:47:50-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/lizard.yaml tool=lizard
2025-09-10T22:47:50-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=pylint toolConfigPath=.codacy/tools-configs/pylint.rc
2025-09-10T22:47:50-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/semgrep.yaml tool=semgrep
2025-09-10T22:49:06-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T22:49:06-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/outputs/extended_network_report.md --format sarif] args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/outputs/extended_network_report.md]
2025-09-10T23:01:25-04:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.356.sha.ee1b0e6 (ee1b0e6) built at 2025-09-04T15:20:51Z
2025-09-10T23:01:25-04:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[/Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_improved_downstream_path.py] command=analyze full_command=[/Users/<USER>/Library/Caches/Codacy/codacy-cli-v2/1.0.0-main.356.sha.ee1b0e6/codacy-cli-v2 analyze /Users/<USER>/Workspace/SoftwareProjects/iguide-hand/src/generate_improved_downstream_path.py --format sarif]
2025-09-10T23:01:25-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=lizard toolConfigPath=.codacy/tools-configs/lizard.yaml
2025-09-10T23:01:25-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/pylint.rc tool=pylint
2025-09-10T23:01:26-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool tool=semgrep toolConfigPath=.codacy/tools-configs/semgrep.yaml
2025-09-10T23:01:29-04:00 [INFO] (codacy-cli-v2/cmd/analyze.go:341) Config file found for tool toolConfigPath=.codacy/tools-configs/trivy.yaml tool=trivy
