analyzer:
    errors:
        avoid_as: warning
        avoid_catches_without_on_clauses: high
        avoid_catching_errors: high
        avoid_double_and_int_checks: warning
        avoid_dynamic_calls: high
        avoid_equals_and_hash_code_on_mutable_classes: high
        avoid_field_initializers_in_const_classes: warning
        avoid_implementing_value_types: high
        avoid_js_rounded_ints: high
        avoid_returning_null: high
        avoid_returning_null_for_future: high
        avoid_slow_async_io: warning
        await_only_futures: warning
        cast_nullable_to_non_nullable: high
        close_sinks: high
        collection_methods_unrelated_type: warning
        conditional_uri_does_not_exist: high
        control_flow_in_finally: high
        discarded_futures: high
        empty_statements: high
        exhaustive_cases: high
        hash_and_equals: high
        invariant_booleans: warning
        iterable_contains_unrelated_type: high
        list_remove_unrelated_type: warning
        no_adjacent_strings_in_list: warning
        no_duplicate_case_values: high
        no_runtimeType_toString: warning
        null_check_on_nullable_type_parameter: high
        null_closures: high
        prefer_bool_in_asserts: info
        prefer_contains: info
        prefer_for_elements_to_map_fromIterable: info
        prefer_is_empty: warning
        recursive_getters: high
        secure_pubspec_urls: high
        sized_box_for_whitespace: info
        test_types_in_equals: high
        throw_in_finally: high
        unawaited_futures: high
        unnecessary_await_in_return: info
        unnecessary_statements: warning
        unrelated_type_equality_checks: warning
        unsafe_html: high
        use_build_context_synchronously: high
        use_colored_box: info
        use_decorated_box: info
        use_string_buffers: warning
        valid_regexps: high
        void_checks: high
linter:
    rules:
        always_declare_return_types: "true"
        always_put_control_body_on_new_line: "true"
        always_put_required_named_parameters_first: "true"
        always_require_non_null_named_parameters: "true"
        always_specify_types: "true"
        always_use_package_imports: "true"
        annotate_overrides: "true"
        avoid_annotating_with_dynamic: "true"
        avoid_bool_literals_in_conditional_expressions: "true"
        avoid_classes_with_only_static_members: "true"
        avoid_empty_else: "true"
        avoid_escaping_inner_quotes: "true"
        avoid_final_parameters: "true"
        avoid_function_literals_in_foreach_calls: "true"
        avoid_init_to_null: "true"
        avoid_multiple_declarations_per_line: "true"
        avoid_null_checks_in_equality_operators: "true"
        avoid_positional_boolean_parameters: "true"
        avoid_print: "true"
        avoid_private_typedef_functions: "true"
        avoid_redundant_argument_values: "true"
        avoid_relative_lib_imports: "true"
        avoid_renaming_method_parameters: "true"
        avoid_return_types_on_setters: "true"
        avoid_returning_null_for_void: "true"
        avoid_returning_this: "true"
        avoid_setters_without_getters: "true"
        avoid_shadowing_type_parameters: "true"
        avoid_single_cascade_in_expression_statements: "true"
        avoid_type_to_string: "true"
        avoid_types_as_parameter_names: "true"
        avoid_types_on_closure_parameters: "true"
        avoid_unnecessary_containers: "true"
        avoid_unused_constructor_parameters: "true"
        avoid_void_async: "true"
        avoid_web_libraries_in_flutter: "true"
        camel_case_extensions: "true"
        camel_case_types: "true"
        cancel_subscriptions: "true"
        cascade_invocations: "true"
        combinators_ordering: "true"
        comment_references: "true"
        constant_identifier_names: "true"
        curly_braces_in_flow_control_structures: "true"
        dangling_library_doc_comments: "true"
        depend_on_referenced_packages: "true"
        deprecated_consistency: "true"
        diagnostic_describe_all_properties: "true"
        directives_ordering: "true"
        do_not_use_environment: "true"
        empty_catches: "true"
        empty_constructor_bodies: "true"
        enable_null_safety: "true"
        eol_at_end_of_file: "true"
        file_names: "true"
        flutter_style_todos: "true"
        implementation_imports: "true"
        implicit_call_tearoffs: "true"
        join_return_with_assignment: "true"
        leading_newlines_in_multiline_strings: "true"
        library_annotations: "true"
        library_names: "true"
        library_prefixes: "true"
        library_private_types_in_public_api: "true"
        lines_longer_than_80_chars: "true"
        literal_only_boolean_expressions: "true"
        missing_whitespace_between_adjacent_strings: "true"
        no_default_cases: "true"
        no_leading_underscores_for_library_prefixes: "true"
        no_leading_underscores_for_local_identifiers: "true"
        no_logic_in_create_state: "true"
        non_constant_identifier_names: "true"
        noop_primitive_operations: "true"
        omit_local_variable_types: "true"
        one_member_abstracts: "true"
        only_throw_errors: "true"
        overridden_fields: "true"
        package_api_docs: "true"
        package_names: "true"
        package_prefixed_library_names: "true"
        parameter_assignments: "true"
        prefer_adjacent_string_concatenation: "true"
        prefer_asserts_in_initializer_lists: "true"
        prefer_asserts_with_message: "true"
        prefer_collection_literals: "true"
        prefer_conditional_assignment: "true"
        prefer_const_constructors: "true"
        prefer_const_constructors_in_immutables: "true"
        prefer_const_declarations: "true"
        prefer_const_literals_to_create_immutables: "true"
        prefer_constructors_over_static_methods: "true"
        prefer_double_quotes: "true"
        prefer_equal_for_default_values: "true"
        prefer_expression_function_bodies: "true"
        prefer_final_fields: "true"
        prefer_final_in_for_each: "true"
        prefer_final_locals: "true"
        prefer_final_parameters: "true"
        prefer_foreach: "true"
        prefer_function_declarations_over_variables: "true"
        prefer_generic_function_type_aliases: "true"
        prefer_if_elements_to_conditional_expressions: "true"
        prefer_if_null_operators: "true"
        prefer_initializing_formals: "true"
        prefer_inlined_adds: "true"
        prefer_int_literals: "true"
        prefer_interpolation_to_compose_strings: "true"
        prefer_is_not_empty: "true"
        prefer_is_not_operator: "true"
        prefer_iterable_whereType: "true"
        prefer_mixin: "true"
        prefer_null_aware_method_calls: "true"
        prefer_null_aware_operators: "true"
        prefer_relative_imports: "true"
        prefer_single_quotes: "true"
        prefer_spread_collections: "true"
        prefer_typing_uninitialized_variables: "true"
        prefer_void_to_null: "true"
        provide_deprecation_message: "true"
        public_member_api_docs: "true"
        require_trailing_commas: "true"
        sized_box_shrink_expand: "true"
        slash_for_doc_comments: "true"
        sort_child_properties_last: "true"
        sort_constructors_first: "true"
        sort_pub_dependencies: "true"
        sort_unnamed_constructors_first: "true"
        super_goes_last: "true"
        tighten_type_of_initializing_formals: "true"
        type_annotate_public_apis: "true"
        type_init_formals: "true"
        unnecessary_brace_in_string_interps: "true"
        unnecessary_const: "true"
        unnecessary_constructor_name: "true"
        unnecessary_final: "true"
        unnecessary_getters_setters: "true"
        unnecessary_lambdas: "true"
        unnecessary_late: "true"
        unnecessary_library_directive: "true"
        unnecessary_new: "true"
        unnecessary_null_aware_assignments: "true"
        unnecessary_null_aware_operator_on_extension_on_nullable: "true"
        unnecessary_null_checks: "true"
        unnecessary_null_in_if_null_operators: "true"
        unnecessary_nullable_for_final_variable_declarations: "true"
        unnecessary_overrides: "true"
        unnecessary_parenthesis: "true"
        unnecessary_raw_strings: "true"
        unnecessary_string_escapes: "true"
        unnecessary_string_interpolations: "true"
        unnecessary_this: "true"
        unnecessary_to_list_in_spreads: "true"
        unreachable_from_main: "true"
        use_enums: "true"
        use_full_hex_values_for_flutter_colors: "true"
        use_function_type_syntax_for_parameters: "true"
        use_if_null_to_convert_nulls_to_bools: "true"
        use_is_even_rather_than_modulo: "true"
        use_key_in_widget_constructors: "true"
        use_late_for_private_fields_and_variables: "true"
        use_named_constants: "true"
        use_raw_strings: "true"
        use_rethrow_when_possible: "true"
        use_setters_to_change_properties: "true"
        use_string_in_part_of_directives: "true"
        use_super_parameters: "true"
        use_test_throws_matchers: "true"
        use_to_and_as_if_applicable: "true"
