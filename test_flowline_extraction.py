#!/usr/bin/env python3
"""
Test script to check the improved flowline extraction from main stem HUCs.
"""

import geopandas as gpd
import numpy as np
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_main_stem_flowlines():
    """Test the main stem flowline extraction."""
    
    output_dir = Path("outputs")
    gdb_path = "data/NHDPLUS_H_1602_HU4_20220412_GDB/NHDPLUS_H_1602_HU4_20220412_GDB.gdb"
    
    # Step 1: Build main stem HUC12 network
    logger.info("Building main stem HUC12 network...")
    
    # Load all HUC12s
    all_hucs = gpd.read_file(gdb_path, layer='WBDHU12')
    huc_dict = {row['huc12']: row for _, row in all_hucs.iterrows()}
    
    # Start from dam HUC12
    dam_huc12_code = '160202040302'  # From previous analysis
    current_huc = dam_huc12_code
    visited = set()
    downstream_hucs = []
    cumulative_distance = 0
    max_distance_km = 150
    
    logger.info("Building main stem downstream path...")
    while current_huc and current_huc not in visited and cumulative_distance < max_distance_km * 1000:
        visited.add(current_huc)
        
        if current_huc in huc_dict:
            huc_row = huc_dict[current_huc]
            downstream_hucs.append(huc_row)
            
            # Calculate approximate distance
            area_km2 = huc_row['areasqkm']
            estimated_length = np.sqrt(area_km2) * 1000
            cumulative_distance += estimated_length
            
            logger.info(f"Added main stem HUC12 {current_huc}: {huc_row['name']} "
                      f"(cumulative distance: {cumulative_distance/1000:.1f}km)")
            
            # Get next downstream HUC
            tohuc = huc_row.get('tohuc', '')
            if tohuc and len(tohuc) >= 12 and tohuc != 'CLOSED BASIN':
                current_huc = tohuc[:12]
            else:
                if tohuc == 'CLOSED BASIN':
                    logger.info("Reached closed basin (Great Salt Lake) - main stem complete")
                else:
                    logger.info(f"No valid tohuc ('{tohuc}') - main stem complete")
                break
        else:
            logger.warning(f"HUC12 {current_huc} not found in dictionary")
            break
    
    # Convert to GeoDataFrame
    main_stem_hucs = gpd.GeoDataFrame(downstream_hucs)
    main_stem_hucs.crs = all_hucs.crs
    
    logger.info(f"Found {len(main_stem_hucs)} main stem HUC12s")
    
    # Step 2: Extract flowlines from main stem HUCs
    logger.info("Extracting flowlines from main stem HUCs...")
    
    # Load NHD flowlines
    all_flowlines = gpd.read_file(gdb_path, layer='NHDFlowline')
    logger.info(f"Loaded {len(all_flowlines)} total flowlines")
    
    # Get HUC12 codes for filtering
    target_huc_codes = set(main_stem_hucs['huc12'].tolist())
    logger.info(f"Main stem HUC12 codes: {target_huc_codes}")
    
    # Use spatial intersection method
    logger.info("Using spatial intersection method...")
    
    # Reproject both datasets to UTM for accurate spatial operations
    main_stem_hucs_utm = main_stem_hucs.to_crs('EPSG:26912')
    all_flowlines_utm = all_flowlines.to_crs('EPSG:26912')
    
    # Spatial intersection with main stem HUC12 polygons
    target_flowlines = gpd.sjoin(all_flowlines_utm, main_stem_hucs_utm, how='inner', predicate='intersects')
    logger.info(f"Spatial intersection found {len(target_flowlines)} flowlines in main stem HUCs")
    
    if len(target_flowlines) == 0:
        logger.error("No flowlines found in main stem HUCs")
        return False
    
    # Calculate total length
    total_length = target_flowlines['lengthkm'].sum()
    logger.info(f"Total length of main stem flowlines: {total_length:.2f} km")
    
    # Show breakdown by HUC12
    logger.info("Flowlines by main stem HUC12:")
    logger.info(f"Available columns: {list(target_flowlines.columns)}")
    
    # Find the HUC12 column
    huc12_column = None
    for col in target_flowlines.columns:
        if 'huc12' in col.lower() and col != 'huc12':  # Avoid the original column
            huc12_column = col
            break
    
    if huc12_column:
        logger.info(f"Using HUC12 column: {huc12_column}")
        for huc_code in target_huc_codes:
            huc_flowlines = target_flowlines[target_flowlines[huc12_column] == huc_code]
            if len(huc_flowlines) > 0:
                huc_length = huc_flowlines['lengthkm'].sum()
                logger.info(f"  {huc_code}: {len(huc_flowlines)} flowlines, {huc_length:.2f} km")
    
    # Save the main stem flowlines
    main_stem_flowlines_file = output_dir / "main_stem_flowlines.shp"
    target_flowlines.to_file(main_stem_flowlines_file)
    logger.info(f"Main stem flowlines saved to: {main_stem_flowlines_file}")
    
    # Save the main stem HUCs
    main_stem_hucs_file = output_dir / "main_stem_huc12s.shp"
    main_stem_hucs.to_file(main_stem_hucs_file)
    logger.info(f"Main stem HUC12s saved to: {main_stem_hucs_file}")
    
    # Create a summary report
    report_content = f"""# Main Stem Flowline Analysis Report

## Main Stem HUC12s
Found {len(main_stem_hucs)} HUC12s in the main stem path:
"""
    
    for i, huc in main_stem_hucs.iterrows():
        report_content += f"- {huc['huc12']}: {huc['name']}\n"
    
    report_content += f"""
## Flowline Statistics
- **Total flowlines in main stem**: {len(target_flowlines)}
- **Total length**: {total_length:.2f} km
- **Average flowline length**: {total_length/len(target_flowlines):.2f} km

## Comparison with Previous Results
- **Previous (limited)**: 15 flowlines, 14.58 km
- **New (main stem)**: {len(target_flowlines)} flowlines, {total_length:.2f} km
- **Improvement**: {len(target_flowlines)/15:.1f}x more flowlines, {total_length/14.58:.1f}x more length

This represents the complete downstream flowline network from Mountain Dell Dam
following the main stem path through the Great Salt Lake drainage basin.
"""
    
    report_file = output_dir / "main_stem_flowline_report.md"
    with open(report_file, 'w') as f:
        f.write(report_content)
    
    logger.info(f"Report saved to: {report_file}")
    logger.info("Main stem flowline extraction completed successfully!")
    
    return True

if __name__ == "__main__":
    success = test_main_stem_flowlines()
    if success:
        print("✅ Main stem flowline extraction successful!")
    else:
        print("❌ Main stem flowline extraction failed!")
