#!/usr/bin/env python3
"""
Generate extended downstream path following connected flowlines from dam location
Similar to net2.shp but with more comprehensive downstream tracing
"""

import subprocess
import logging
import geopandas as gpd
import pandas as pd
from pathlib import Path
from shapely.geometry import Point

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_extended_downstream_path():
    """Generate extended downstream path following connected flowlines"""
    
    output_dir = Path("outputs")
    
    # Load dam location
    logger.info("Loading dam location...")
    dam_location = gpd.read_file("data/mountain_dell_dam_location.shp")
    dam_utm = dam_location.to_crs("EPSG:26912")
    dam_point = dam_utm.geometry.iloc[0]
    logger.info(f"Dam location: {dam_point}")
    
    # Load all flowlines from expanded network
    logger.info("Loading all flowlines from expanded network...")
    all_flowlines = gpd.read_file(output_dir / "target_flowlines.shp")
    logger.info(f"Found {len(all_flowlines)} total flowlines")
    
    # Load NHD Plus flow connectivity
    logger.info("Loading NHD Plus flow connectivity...")
    gdb_path = "data/NHDPLUS_H_1602_HU4_20220412_GDB/NHDPLUS_H_1602_HU4_20220412_GDB.gdb"
    flow_data = gpd.read_file(gdb_path, layer='NHDPlusFlow')
    logger.info(f"Loaded {len(flow_data)} flow connections")
    
    # Step 1: Find flowline closest to dam
    logger.info("Finding flowline closest to dam...")
    distances = all_flowlines.geometry.distance(dam_point)
    closest_idx = distances.idxmin()
    closest_flowline = all_flowlines.loc[closest_idx]
    closest_distance = distances.iloc[closest_idx]
    
    logger.info(f"Closest flowline to dam: nhdplusid {closest_flowline['nhdplusid_']}")
    logger.info(f"Distance to dam: {closest_distance:.2f} meters")
    
    # Step 2: Extended downstream tracing with multiple strategies
    logger.info("Starting extended downstream tracing...")
    
    downstream_flowlines = []
    visited_nhdplusids = set()
    
    # Add the starting flowline
    downstream_flowlines.append(closest_flowline)
    visited_nhdplusids.add(closest_flowline['nhdplusid_'])
    current_nhdplusid = closest_flowline['nhdplusid_']
    
    # Strategy 1: Follow main connectivity chain
    logger.info("Strategy 1: Following main connectivity chain...")
    iteration = 0
    max_iterations = 100  # Prevent infinite loops
    
    while iteration < max_iterations:
        iteration += 1
        
        # Find the next downstream flowline using flow connectivity
        downstream_flows = flow_data[flow_data['fromnhdpid'] == current_nhdplusid]
        
        if len(downstream_flows) == 0:
            logger.info(f"Main chain ended at iteration {iteration} - no downstream connections")
            break
            
        # Get the downstream nhdplusid
        next_nhdplusid = downstream_flows.iloc[0]['tonhdpid']
        
        # Skip if we've already visited this flowline
        if next_nhdplusid in visited_nhdplusids:
            logger.info(f"Cycle detected at iteration {iteration} - stopping main chain")
            break
            
        # Find flowline with this nhdplusid
        next_flowlines = all_flowlines[all_flowlines['nhdplusid_'] == next_nhdplusid]
        
        if len(next_flowlines) == 0:
            logger.info(f"No flowline found for downstream nhdplusid {next_nhdplusid}")
            break
            
        next_flowline = next_flowlines.iloc[0]
        downstream_flowlines.append(next_flowline)
        visited_nhdplusids.add(next_nhdplusid)
        current_nhdplusid = next_nhdplusid
        
        logger.info(f"Added downstream flowline: nhdplusid {next_nhdplusid}")
    
    logger.info(f"Main chain found {len(downstream_flowlines)} flowlines")
    
    # Strategy 2: Find major tributaries that join the main stem
    logger.info("Strategy 2: Finding major tributaries...")
    
    # For each flowline in the main stem, find flowlines that flow into it
    main_stem_ids = [fl['nhdplusid_'] for fl in downstream_flowlines]
    
    for main_stem_id in main_stem_ids:
        # Find flowlines that flow into this main stem flowline
        tributary_flows = flow_data[flow_data['tonhdpid'] == main_stem_id]
        
        for _, trib_flow in tributary_flows.iterrows():
            trib_nhdplusid = trib_flow['fromnhdpid']
            
            # Skip if already in our collection
            if trib_nhdplusid in visited_nhdplusids:
                continue
                
            # Find the tributary flowline
            trib_flowlines = all_flowlines[all_flowlines['nhdplusid_'] == trib_nhdplusid]
            
            if len(trib_flowlines) > 0:
                trib_flowline = trib_flowlines.iloc[0]
                
                # Only add significant tributaries (length > 1km)
                if trib_flowline.get('lengthkm', 0) > 1.0:
                    downstream_flowlines.append(trib_flowline)
                    visited_nhdplusids.add(trib_nhdplusid)
                    logger.info(f"Added major tributary: nhdplusid {trib_nhdplusid} (length: {trib_flowline.get('lengthkm', 0):.2f} km)")
    
    logger.info(f"Total flowlines after adding tributaries: {len(downstream_flowlines)}")
    
    # Strategy 3: Spatial connectivity - find nearby flowlines that connect
    logger.info("Strategy 3: Finding spatially connected flowlines...")
    
    # Create a buffer around existing flowlines to find nearby connections
    existing_geoms = [fl.geometry for fl in downstream_flowlines]
    
    for flowline in downstream_flowlines[:]:  # Copy list to avoid modification during iteration
        # Find flowlines that are very close (within 100m) and not already included
        buffer = flowline.geometry.buffer(100)  # 100m buffer
        
        nearby_flowlines = all_flowlines[all_flowlines.geometry.intersects(buffer)]
        
        for _, nearby in nearby_flowlines.iterrows():
            if nearby['nhdplusid_'] not in visited_nhdplusids:
                # Check if this flowline is significant (length > 0.5km)
                if nearby.get('lengthkm', 0) > 0.5:
                    downstream_flowlines.append(nearby)
                    visited_nhdplusids.add(nearby['nhdplusid_'])
                    logger.info(f"Added spatially connected flowline: nhdplusid {nearby['nhdplusid_']} (length: {nearby.get('lengthkm', 0):.2f} km)")
                    
                    # Limit spatial additions to prevent too many
                    if len(downstream_flowlines) > 50:
                        break
        
        if len(downstream_flowlines) > 50:
            break
    
    logger.info(f"Final total flowlines: {len(downstream_flowlines)}")
    
    # Step 3: Create GeoDataFrame and sort by distance from dam
    logger.info("Creating final downstream flowlines dataset...")
    
    downstream_gdf = gpd.GeoDataFrame(downstream_flowlines)
    
    # Calculate distance from dam for sorting
    distances = []
    for _, flowline in downstream_gdf.iterrows():
        distance = dam_point.distance(flowline.geometry)
        distances.append(distance)
    
    downstream_gdf['dam_distance'] = distances
    downstream_gdf = downstream_gdf.sort_values('dam_distance')
    
    # Add LINKNO field (1-based, closest to dam = 1)
    downstream_gdf['LINKNO'] = range(1, len(downstream_gdf) + 1)
    
    logger.info(f"Closest flowline: {downstream_gdf.iloc[0]['dam_distance']:.1f}m from dam")
    logger.info(f"Farthest flowline: {downstream_gdf.iloc[-1]['dam_distance']:.1f}m from dam")
    
    # Step 4: Save the extended downstream flowlines
    extended_file = output_dir / "extended_downstream_flowlines.shp"
    downstream_gdf.to_file(extended_file)
    logger.info(f"Saved {len(downstream_gdf)} extended downstream flowlines to {extended_file}")
    
    # Step 5: Create raster from extended flowlines
    logger.info("Creating extended downstream stream network raster...")
    
    stream_raster_file = output_dir / "extended_stream_network.tif"
    
    rasterize_cmd = [
        'gdal_rasterize',
        '-a', 'LINKNO',
        '-tr', '30', '30',
        '-a_nodata', '-9999',
        '-te', '325034.9880469', '4494308.282', '451934.988', '4620210.564',
        '-ot', 'Int32',
        str(extended_file),
        str(stream_raster_file)
    ]
    
    result = subprocess.run(rasterize_cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        logger.error(f"Rasterize failed: {result.stderr}")
        return False
    
    logger.info("✅ Extended stream network raster created")
    
    # Step 6: Copy to main output files
    logger.info("Copying to main output files...")
    
    import shutil
    
    # Copy raster
    shutil.copy2(stream_raster_file, output_dir / "stream_network.tif")
    
    # Copy vector files
    for ext in ['.shp', '.shx', '.dbf', '.prj', '.cpg']:
        src_file = extended_file.with_suffix(ext)
        dst_file = (output_dir / "stream_network").with_suffix(ext)
        if src_file.exists():
            shutil.copy2(src_file, dst_file)
    
    logger.info("✅ Copied extended stream network files")
    
    # Step 7: Create summary report
    total_length = downstream_gdf['lengthkm'].sum()
    avg_distance = downstream_gdf['dam_distance'].mean() / 1000
    
    report = f"""
# Extended Downstream Stream Network Summary

## Network Statistics:
- **Total flowlines**: {len(downstream_gdf)}
- **Total length**: {total_length:.2f} km
- **Average distance from dam**: {avg_distance:.2f} km
- **Distance range**: {downstream_gdf.iloc[0]['dam_distance']:.1f}m to {downstream_gdf.iloc[-1]['dam_distance']:.1f}m

## Tracing Strategy Results:
- **Main stem chain**: Connected downstream flowlines following NHD connectivity
- **Major tributaries**: Significant tributaries (>1km) joining the main stem
- **Spatial connections**: Nearby flowlines (>0.5km) within 100m buffer

## Comparison with Reference:
- **Reference net2.shp**: 7 connected segments
- **Our extended network**: {len(downstream_gdf)} connected segments
- **Structure**: Similar connected path approach, more comprehensive coverage

## Key Features:
- ✅ Follows connected downstream path from dam (like net2.shp)
- ✅ Includes major tributaries and spatial connections
- ✅ Prioritized by distance from dam (LINKNO 1 = closest)
- ✅ Proper LineString geometry
- ✅ Rich NHD attributes preserved

This extended network follows the same approach as net2.shp but provides more 
comprehensive coverage of the downstream flow paths from Mountain Dell Dam.
"""
    
    report_file = output_dir / "extended_network_report.md"
    with open(report_file, 'w') as f:
        f.write(report)
    
    logger.info(f"✅ Created extended network report: {report_file}")
    
    return True

if __name__ == "__main__":
    logger.info("Starting extended downstream path generation...")
    success = generate_extended_downstream_path()
    if success:
        logger.info("Extended downstream path generation completed successfully!")
    else:
        logger.error("Extended downstream path generation failed!")
