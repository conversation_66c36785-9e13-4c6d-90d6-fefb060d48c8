#!/usr/bin/env python3
"""
Generate flowlines from a dam location using GDAL, TauDEM, and NHD data.

This script implements a complete workflow to:
1. Find HUC12 for dam location
2. Build downstream HUC12 connectivity network
3. Process DEM data with TauDEM
4. Generate flowlines and stream networks

Author: Hydrologist Assistant
Date: 2025-09-10
"""

import os
import sys
import subprocess
from pathlib import Path
import geopandas as gpd
import numpy as np
from shapely.geometry import Point
import argparse
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FlowlineGenerator:
    """Main class for generating flowlines from dam location."""

    def __init__(self, dam_shapefile, nhd_gdb, dem_file, output_dir):
        self.dam_shapefile = dam_shapefile
        self.nhd_gdb = nhd_gdb
        self.dem_file = dem_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # Set TauDEM path
        os.environ['PATH'] = f"{os.path.expanduser('~/taudem')}:{os.environ['PATH']}"

        # Initialize data containers
        self.dam_location = None
        self.dam_huc12 = None
        self.downstream_hucs = []
        self.target_extent = None
        self.huc12_data = None
        self.target_crs = "EPSG:26912"  # UTM Zone 12N

    def load_dam_location(self):
        """Load dam location from shapefile."""
        logger.info("Loading dam location...")
        try:
            self.dam_location = gpd.read_file(self.dam_shapefile)
            if len(self.dam_location) == 0:
                raise ValueError("No features found in dam shapefile")

            # Get the first (and should be only) dam point
            dam_point = self.dam_location.iloc[0]
            logger.info(f"Dam loaded: {dam_point.get('Dam_Name', 'Unknown')} at {dam_point.geometry}")
            return True

        except Exception as e:
            logger.error(f"Error loading dam location: {e}")
            return False

    def find_dam_huc12(self):
        """Find the HUC12 that contains the dam location."""
        logger.info("Finding HUC12 for dam location...")
        try:
            # Load HUC12 boundaries
            self.huc12_data = gpd.read_file(self.nhd_gdb, layer='WBDHU12')
            huc12_gdf = self.huc12_data

            # Ensure same CRS
            if self.dam_location.crs != huc12_gdf.crs:
                dam_reproj = self.dam_location.to_crs(huc12_gdf.crs)
            else:
                dam_reproj = self.dam_location

            # Find intersecting HUC12
            dam_point = dam_reproj.iloc[0].geometry
            intersecting = huc12_gdf[huc12_gdf.intersects(dam_point)]

            if len(intersecting) == 0:
                logger.error("Dam location does not intersect any HUC12")
                return False

            self.dam_huc12 = intersecting.iloc[0]
            logger.info(f"Dam is in HUC12: {self.dam_huc12['huc12']} - {self.dam_huc12['name']}")

            # Save dam HUC12 for reference
            dam_huc_file = self.output_dir / "dam_huc12.shp"
            intersecting.to_file(dam_huc_file)
            logger.info(f"Dam HUC12 saved to: {dam_huc_file}")

            return True

        except Exception as e:
            logger.error(f"Error finding dam HUC12: {e}")
            return False

    def check_dam_in_nhd_points(self):
        """Check if dam location exists in NHD points database."""
        logger.info("Checking if dam exists in NHD points...")
        try:
            # Load NHD points
            nhd_points = gpd.read_file(self.nhd_gdb, layer='NHDPoint')

            # Ensure same CRS
            if self.dam_location.crs != nhd_points.crs:
                dam_reproj = self.dam_location.to_crs(nhd_points.crs)
            else:
                dam_reproj = self.dam_location

            # Create buffer around dam (100m)
            dam_buffer = dam_reproj.buffer(100)  # 100m buffer

            # Find points within buffer
            nearby_points = nhd_points[nhd_points.intersects(dam_buffer.iloc[0])]

            if len(nearby_points) > 0:
                logger.info(f"Found {len(nearby_points)} NHD points near dam location")
                # Save nearby points
                nearby_file = self.output_dir / "nearby_nhd_points.shp"
                nearby_points.to_file(nearby_file)
                logger.info(f"Nearby NHD points saved to: {nearby_file}")
            else:
                logger.info("No NHD points found near dam location")

            return True

        except Exception as e:
            logger.error(f"Error checking NHD points: {e}")
            return False

    def build_downstream_huc12_network(self, max_distance_km=100):
        """Build downstream HUC12 connectivity network within specified distance."""
        logger.info(f"Building downstream HUC12 network (max {max_distance_km}km)...")
        try:
            # Load all HUC12s
            all_hucs = gpd.read_file(self.nhd_gdb, layer='WBDHU12')

            # Create lookup dictionary for HUC12s
            huc_dict = {row['huc12']: row for _, row in all_hucs.iterrows()}

            # Start from dam HUC12
            current_huc = self.dam_huc12['huc12']
            visited = set()
            downstream_hucs = []
            cumulative_distance = 0

            # Traverse downstream - MAIN STEM ONLY for flowline generation
            logger.info("Building main stem downstream path...")
            while current_huc and current_huc not in visited and cumulative_distance < max_distance_km * 1000:
                visited.add(current_huc)

                if current_huc in huc_dict:
                    huc_row = huc_dict[current_huc]
                    downstream_hucs.append(huc_row)

                    # Calculate approximate distance (using area as proxy)
                    # This is a simplification - in reality you'd use more sophisticated methods
                    area_km2 = huc_row['areasqkm']
                    estimated_length = np.sqrt(area_km2) * 1000  # Rough estimate
                    cumulative_distance += estimated_length

                    logger.info(f"Added main stem HUC12 {current_huc}: {huc_row['name']} "
                              f"(cumulative distance: {cumulative_distance/1000:.1f}km)")

                    # Get next downstream HUC
                    tohuc = huc_row.get('tohuc', '')
                    if tohuc and len(tohuc) >= 12 and tohuc != 'CLOSED BASIN':
                        current_huc = tohuc[:12]  # Take first 12 characters for HUC12
                    else:
                        if tohuc == 'CLOSED BASIN':
                            logger.info(f"Reached closed basin (Great Salt Lake) - main stem complete")
                        else:
                            logger.info(f"No valid tohuc ('{tohuc}') - main stem complete")
                        break
                else:
                    logger.warning(f"HUC12 {current_huc} not found in dictionary")
                    break

            # Convert to GeoDataFrame
            if downstream_hucs:
                self.downstream_hucs = gpd.GeoDataFrame(downstream_hucs)
                # Ensure CRS is set
                if self.downstream_hucs.crs is None:
                    self.downstream_hucs.crs = self.huc12_data.crs

                # Save downstream HUCs (main stem only)
                downstream_file = self.output_dir / "downstream_huc12s_main_stem.shp"
                self.downstream_hucs.to_file(downstream_file)
                logger.info(f"Found {len(self.downstream_hucs)} main stem HUC12s")
                logger.info(f"Main stem HUC12s saved to: {downstream_file}")

                # Also save as the main downstream file for compatibility
                main_downstream_file = self.output_dir / "downstream_huc12s.shp"
                self.downstream_hucs.to_file(main_downstream_file)
                logger.info(f"Main stem HUC12s also saved to: {main_downstream_file}")

                return True
            else:
                logger.error("No downstream HUCs found")
                return False

        except Exception as e:
            logger.error(f"Error building downstream network: {e}")
            return False

    def create_study_area_extent(self, buffer_km=2):
        """Create buffered extent around downstream HUCs for raster processing."""
        logger.info(f"Creating study area with {buffer_km}km buffer...")
        try:
            if self.downstream_hucs is None or len(self.downstream_hucs) == 0:
                logger.error("No downstream HUCs available")
                return False

            # Ensure we're in a projected CRS for buffering
            if self.downstream_hucs.crs.is_geographic:
                # Reproject to UTM Zone 12N for accurate buffering
                downstream_utm = self.downstream_hucs.to_crs(self.target_crs)
            else:
                downstream_utm = self.downstream_hucs

            # Create buffer around HUCs
            buffer_m = buffer_km * 1000
            buffered_geoms = downstream_utm.geometry.buffer(buffer_m)

            logger.info(f"Buffered geometries type: {type(buffered_geoms)}")
            logger.info(f"Buffered geometries length: {len(buffered_geoms)}")

            # Get union of all buffered areas
            from shapely.ops import unary_union
            geom_list = buffered_geoms.tolist()
            logger.info(f"Geometry list type: {type(geom_list)}")
            logger.info(f"Geometry list length: {len(geom_list)}")

            # Try a different approach - use the GeoSeries directly
            try:
                study_area = buffered_geoms.union_all()
                logger.info("Study area created successfully using GeoSeries.union_all()")
                logger.info(f"Study area type: {type(study_area)}")
                logger.info(f"Study area: {study_area}")
            except Exception as e:
                logger.error(f"Failed with GeoSeries.union_all(): {e}")
                # Fallback to shapely unary_union
                try:
                    study_area = unary_union(geom_list)
                    logger.info("Study area created successfully using shapely.unary_union")
                    logger.info(f"Study area type: {type(study_area)}")
                except Exception as e2:
                    logger.error(f"Failed with shapely.unary_union: {e2}")
                    raise e2

            # Convert to GeoDataFrame
            study_area_gdf = gpd.GeoDataFrame({'id': [1]}, geometry=[study_area], crs=downstream_utm.crs)

            # Save study area
            study_area_file = self.output_dir / "study_area.shp"
            study_area_gdf.to_file(study_area_file)
            logger.info(f"Study area saved to: {study_area_file}")

            # Get extent for raster processing
            bounds = study_area.bounds
            self.target_extent = {
                'xmin': bounds[0],
                'ymin': bounds[1],
                'xmax': bounds[2],
                'ymax': bounds[3]
            }

            logger.info(f"Study area extent: {self.target_extent}")
            return True

        except Exception as e:
            logger.error(f"Error creating study area: {e}")
            return False

    def prepare_dem_data(self, target_resolution=30):
        """Prepare DEM data for TauDEM processing."""
        logger.info("Preparing DEM data...")
        try:
            # Check if we have target extent
            if self.target_extent is None:
                logger.error("No target extent defined")
                return False

            # Define output files
            dem_utm_file = self.output_dir / "dem_utm12n.tif"
            dem_subset_file = self.output_dir / "dem_subset.tif"

            # First, reproject DEM to UTM 12N (EPSG:26912)
            logger.info("Reprojecting DEM to UTM 12N...")
            reproject_cmd = [
                'gdalwarp',
                '-t_srs', 'EPSG:26912',
                '-tr', str(target_resolution), str(target_resolution),
                '-r', 'bilinear',
                '-of', 'GTiff',
                self.dem_file,
                str(dem_utm_file)
            ]

            result = subprocess.run(reproject_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"DEM reprojection failed: {result.stderr}")
                return False

            logger.info(f"DEM reprojected to: {dem_utm_file}")

            # Get study area extent in UTM coordinates
            # First reproject study area to UTM 12N
            study_area_file = self.output_dir / "study_area.shp"
            study_area_utm_file = self.output_dir / "study_area_utm.shp"

            reproject_vector_cmd = [
                'ogr2ogr',
                '-t_srs', 'EPSG:26912',
                str(study_area_utm_file),
                str(study_area_file)
            ]

            result = subprocess.run(reproject_vector_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"Study area reprojection failed: {result.stderr}")
                return False

            # Get extent of reprojected study area
            study_area_utm = gpd.read_file(study_area_utm_file)
            bounds = study_area_utm.total_bounds

            # Subset DEM to study area with buffer
            logger.info("Subsetting DEM to study area...")
            subset_cmd = [
                'gdalwarp',
                '-te', str(bounds[0]), str(bounds[1]), str(bounds[2]), str(bounds[3]),
                '-tr', str(target_resolution), str(target_resolution),
                '-r', 'bilinear',
                '-of', 'GTiff',
                str(dem_utm_file),
                str(dem_subset_file)
            ]

            result = subprocess.run(subset_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"DEM subsetting failed: {result.stderr}")
                return False

            logger.info(f"DEM subset created: {dem_subset_file}")

            # Store the subset DEM file for TauDEM processing
            self.dem_subset_file = dem_subset_file

            return True

        except Exception as e:
            logger.error(f"Error preparing DEM data: {e}")
            return False

    def run_taudem_preprocessing(self):
        """Run TauDEM preprocessing steps."""
        logger.info("Running TauDEM preprocessing...")
        try:
            if not hasattr(self, 'dem_subset_file'):
                logger.error("No subset DEM file available")
                return False

            # Define output files
            dem_filled_file = self.output_dir / "dem_filled.tif"
            flow_dir_file = self.output_dir / "flow_directions.tif"
            flow_acc_file = self.output_dir / "flow_accumulation.tif"

            # Step 1: Fill pits
            logger.info("Filling pits with TauDEM pitremove...")
            pitremove_cmd = [
                'mpiexec', '-n', '1', 'pitremove',
                '-z', str(self.dem_subset_file),
                '-fel', str(dem_filled_file)
            ]

            result = subprocess.run(pitremove_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"Pitremove failed: {result.stderr}")
                return False

            logger.info(f"Filled DEM created: {dem_filled_file}")

            # Step 2: Generate flow directions
            logger.info("Generating flow directions with TauDEM d8flowdir...")
            d8flowdir_cmd = [
                'mpiexec', '-n', '1', 'd8flowdir',
                '-fel', str(dem_filled_file),
                '-p', str(flow_dir_file),
                '-sd8', str(flow_acc_file)
            ]

            result = subprocess.run(d8flowdir_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"D8flowdir failed: {result.stderr}")
                return False

            logger.info(f"Flow directions created: {flow_dir_file}")
            logger.info(f"Flow accumulation created: {flow_acc_file}")

            # Store file paths for later use
            self.dem_filled_file = dem_filled_file
            self.flow_dir_file = flow_dir_file
            self.flow_acc_file = flow_acc_file

            return True

        except Exception as e:
            logger.error(f"Error in TauDEM preprocessing: {e}")
            return False

    def extract_flowlines_and_endpoints(self):
        """Extract flowlines from target HUCs and generate endpoint outlets."""
        logger.info("Extracting flowlines from main stem HUCs...")
        try:
            # Load NHD flowlines
            all_flowlines = gpd.read_file(self.nhd_gdb, layer='NHDFlowline')

            # Get HUC12 codes for filtering (main stem only)
            target_huc_codes = set(self.downstream_hucs['huc12'].tolist())
            logger.info(f"Main stem HUC12 codes: {target_huc_codes}")

            # FORCE spatial intersection method since reachcode filtering is broken for this area
            logger.info("Using spatial intersection method (reachcode filtering is unreliable)")

            # Reproject both datasets to UTM for accurate spatial operations
            downstream_hucs_utm = self.downstream_hucs.to_crs('EPSG:26912')
            all_flowlines_utm = all_flowlines.to_crs('EPSG:26912')

            # Spatial intersection with main stem HUC12 polygons
            target_flowlines = gpd.sjoin(all_flowlines_utm, downstream_hucs_utm, how='inner', predicate='intersects')
            logger.info(f"Spatial intersection found {len(target_flowlines)} flowlines in main stem HUCs")

            if len(target_flowlines) == 0:
                logger.error("No flowlines found in main stem HUCs using spatial intersection")
                return False

            # Calculate total length
            total_length = target_flowlines['lengthkm'].sum()
            logger.info(f"Total length of main stem flowlines: {total_length:.2f} km")

            # Show breakdown by HUC12
            logger.info("Flowlines by main stem HUC12:")
            # Check what columns are available after spatial join
            logger.info(f"Available columns after spatial join: {list(target_flowlines.columns)}")

            # Find the HUC12 column (it might be named differently after spatial join)
            huc12_column = None
            for col in target_flowlines.columns:
                if 'huc12' in col.lower():
                    huc12_column = col
                    break

            if huc12_column:
                logger.info(f"Using HUC12 column: {huc12_column}")
                for huc_code in target_huc_codes:
                    huc_flowlines = target_flowlines[target_flowlines[huc12_column] == huc_code]
                    if len(huc_flowlines) > 0:
                        huc_length = huc_flowlines['lengthkm'].sum()
                        logger.info(f"  {huc_code}: {len(huc_flowlines)} flowlines, {huc_length:.2f} km")
                    else:
                        logger.warning(f"  {huc_code}: No flowlines found")
            else:
                logger.warning("No HUC12 column found after spatial join - skipping breakdown")

            logger.info(f"Found {len(target_flowlines)} flowlines in main stem HUCs")

            # Reproject to UTM 12N for consistency (if not already done)
            if target_flowlines.crs != 'EPSG:26912':
                target_flowlines_utm = target_flowlines.to_crs('EPSG:26912')
            else:
                target_flowlines_utm = target_flowlines

            # Save flowlines
            flowlines_file = self.output_dir / "target_flowlines.shp"
            target_flowlines_utm.to_file(flowlines_file)
            logger.info(f"Target flowlines saved to: {flowlines_file}")

            # Extract downstream endpoints of flowlines
            endpoints = []
            for _, flowline in target_flowlines_utm.iterrows():
                if flowline.geometry:
                    try:
                        # Handle both LineString and MultiLineString geometries
                        if flowline.geometry.geom_type == 'LineString':
                            # Simple LineString - get last point
                            coords = list(flowline.geometry.coords)
                            if len(coords) > 0:
                                endpoint = Point(coords[-1])
                                endpoints.append({
                                    'geometry': endpoint,
                                    'nhdplusid': flowline.get('nhdplusid', ''),
                                    'reachcode': flowline.get('reachcode', '')
                                })
                        elif flowline.geometry.geom_type == 'MultiLineString':
                            # MultiLineString - get last point of last linestring
                            for line in flowline.geometry.geoms:
                                coords = list(line.coords)
                                if len(coords) > 0:
                                    endpoint = Point(coords[-1])
                                    endpoints.append({
                                        'geometry': endpoint,
                                        'nhdplusid': flowline.get('nhdplusid', ''),
                                        'reachcode': flowline.get('reachcode', '')
                                    })
                    except Exception as e:
                        logger.warning(f"Error processing flowline geometry: {e}")
                        continue

            if endpoints:
                endpoints_gdf = gpd.GeoDataFrame(endpoints, crs='EPSG:26912')
                endpoints_file = self.output_dir / "flowline_endpoints.shp"
                endpoints_gdf.to_file(endpoints_file)
                logger.info(f"Generated {len(endpoints)} flowline endpoints")
                logger.info(f"Endpoints saved to: {endpoints_file}")

                self.endpoints_file = endpoints_file
                return True
            else:
                logger.error("No valid flowline endpoints generated")
                return False

        except Exception as e:
            logger.error(f"Error extracting flowlines: {e}")
            return False

    def run_taudem_streamnet(self):
        """Run TauDEM streamnet to generate final stream network."""
        logger.info("Running TauDEM streamnet...")
        try:
            if not all(hasattr(self, attr) for attr in ['flow_dir_file', 'endpoints_file']):
                logger.error("Missing required files for streamnet")
                return False

            # Define output files
            stream_raster_file = self.output_dir / "stream_network.tif"
            stream_vector_file = self.output_dir / "stream_network.shp"
            watershed_file = self.output_dir / "watersheds.tif"

            # Run streamnet - TauDEM streamnet has different syntax
            # Let's use a simpler approach first - generate stream network from flow accumulation
            logger.info("Generating stream network with TauDEM threshold...")

            # First, create a stream raster using threshold on flow accumulation
            threshold_cmd = [
                'mpiexec', '-n', '1', 'threshold',
                '-ssa', str(self.flow_acc_file),
                '-src', str(stream_raster_file),
                '-thresh', '1000'  # Threshold for stream initiation
            ]

            result = subprocess.run(threshold_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"Threshold failed: {result.stderr}")
                return False

            logger.info(f"Stream raster created: {stream_raster_file}")

            # Now run streamnet with the correct syntax
            logger.info("Generating stream network with TauDEM streamnet...")
            streamnet_cmd = [
                'mpiexec', '-n', '1', 'streamnet',
                '-p', str(self.flow_dir_file),
                '-fel', str(self.dem_filled_file),
                '-ad8', str(self.flow_acc_file),
                '-src', str(stream_raster_file),
                '-ord', str(self.output_dir / "stream_order.tif"),
                '-tree', str(self.output_dir / "tree.txt"),
                '-coord', str(self.output_dir / "coordinates.txt"),
                '-net', str(stream_vector_file),
                '-w', str(watershed_file)
            ]

            result = subprocess.run(streamnet_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"Streamnet failed: {result.stderr}")
                return False

            logger.info(f"Stream network raster created: {stream_raster_file}")
            logger.info(f"Stream network vector created: {stream_vector_file}")
            logger.info(f"Watersheds created: {watershed_file}")

            # Store output files
            self.stream_raster_file = stream_raster_file
            self.stream_vector_file = stream_vector_file
            self.watershed_file = watershed_file

            return True

        except Exception as e:
            logger.error(f"Error in TauDEM streamnet: {e}")
            return False

    def generate_final_outputs(self):
        """Generate final flowline products and validation."""
        logger.info("Generating final outputs...")
        try:
            # Copy flow direction file as final output
            final_flow_dir = self.output_dir / "final_flow_directions.tif"
            subprocess.run(['cp', str(self.flow_dir_file), str(final_flow_dir)])

            # Generate summary report
            report_file = self.output_dir / "processing_report.txt"
            with open(report_file, 'w') as f:
                f.write("FLOWLINE GENERATION PROCESSING REPORT\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Dam Location: {self.dam_location.iloc[0].get('Dam_Name', 'Unknown')}\n")
                f.write(f"Dam HUC12: {self.dam_huc12['huc12']} - {self.dam_huc12['name']}\n")
                f.write(f"Number of downstream HUC12s: {len(self.downstream_hucs)}\n")
                f.write("Processing resolution: 30m\n")
                f.write("Output projection: UTM Zone 12N (EPSG:26912)\n\n")

                f.write("OUTPUT FILES:\n")
                f.write(f"- Flow directions: {final_flow_dir}\n")
                f.write(f"- Stream network (raster): {self.stream_raster_file}\n")
                f.write(f"- Stream network (vector): {self.stream_vector_file}\n")
                f.write(f"- Watersheds: {self.watershed_file}\n")
                f.write(f"- Target flowlines: {self.output_dir / 'target_flowlines.shp'}\n")
                f.write(f"- Study area: {self.output_dir / 'study_area.shp'}\n")

            logger.info(f"Processing report saved to: {report_file}")
            logger.info("Flowline generation completed successfully!")

            return True

        except Exception as e:
            logger.error(f"Error generating final outputs: {e}")
            return False

    def run_complete_workflow(self):
        """Run the complete flowline generation workflow."""
        logger.info("Starting complete flowline generation workflow...")

        # Phase 1: Dam Location Analysis and HUC12 Identification
        if not self.load_dam_location():
            return False
        if not self.find_dam_huc12():
            return False
        if not self.check_dam_in_nhd_points():
            return False

        # Phase 2: Downstream HUC12 Assembly
        if not self.build_downstream_huc12_network():
            return False
        if not self.create_study_area_extent():
            return False

        # Phase 3: Raster Data Preparation
        if not self.prepare_dem_data():
            return False

        # Phase 4: TauDEM Processing
        if not self.run_taudem_preprocessing():
            return False

        # Phase 5: Flowline Processing
        if not self.extract_flowlines_and_endpoints():
            return False

        # Phase 6: Stream Network Generation
        if not self.run_taudem_streamnet():
            return False

        # Phase 7: Final Output Generation
        if not self.generate_final_outputs():
            return False

        logger.info("Complete workflow finished successfully!")
        return True


def main():
    """Main function for command-line execution."""
    parser = argparse.ArgumentParser(
        description="Generate flowlines from dam location using GDAL, TauDEM, and NHD data"
    )
    parser.add_argument(
        "--dam-shapefile",
        default="data/mountain_dell_dam_location.shp",
        help="Path to dam location shapefile"
    )
    parser.add_argument(
        "--nhd-gdb",
        default="data/NHDPLUS_H_1602_HU4_20220412_GDB/NHDPLUS_H_1602_HU4_20220412_GDB.gdb",
        help="Path to NHD Plus geodatabase"
    )
    parser.add_argument(
        "--dem-file",
        default="data/elevNHDm.tif",
        help="Path to DEM file"
    )
    parser.add_argument(
        "--output-dir",
        default="outputs",
        help="Output directory for results"
    )
    parser.add_argument(
        "--max-distance",
        type=float,
        default=100.0,
        help="Maximum downstream distance in kilometers"
    )

    args = parser.parse_args()

    # Create flowline generator
    generator = FlowlineGenerator(
        dam_shapefile=args.dam_shapefile,
        nhd_gdb=args.nhd_gdb,
        dem_file=args.dem_file,
        output_dir=args.output_dir
    )

    # Run workflow
    success = generator.run_complete_workflow()

    if success:
        logger.info("Flowline generation completed successfully!")
        sys.exit(0)
    else:
        logger.error("Flowline generation failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()