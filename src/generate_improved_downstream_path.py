#!/usr/bin/env python3
"""
Generate improved downstream path using NHDPlusFlowlineVAA for better connectivity
"""

import subprocess
import logging
import geopandas as gpd
import pandas as pd
from pathlib import Path
from shapely.geometry import Point

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_improved_downstream_path(max_stream_order=6, min_stream_order=1, max_distance_km=200):
    """
    Generate improved downstream path using NHDPlusFlowlineVAA
    
    Parameters:
    - max_stream_order: Maximum stream order to include (higher = larger streams)
    - min_stream_order: Minimum stream order to include 
    - max_distance_km: Maximum distance from dam to trace
    """
    
    output_dir = Path("outputs")
    
    # Load dam location
    logger.info("Loading dam location...")
    dam_location = gpd.read_file("data/mountain_dell_dam_location.shp")
    dam_utm = dam_location.to_crs("EPSG:26912")
    dam_point = dam_utm.geometry.iloc[0]
    logger.info(f"Dam location: {dam_point}")
    
    # Load NHD Plus data
    logger.info("Loading NHD Plus data...")
    gdb_path = "data/NHDPLUS_H_1602_HU4_20220412_GDB/NHDPLUS_H_1602_HU4_20220412_GDB.gdb"
    
    # Load flowlines with geometry
    all_flowlines = gpd.read_file(gdb_path, layer='NHDFlowline')
    logger.info(f"Loaded {len(all_flowlines)} NHD flowlines")
    
    # Load flowline attributes (VAA - Value Added Attributes)
    flowline_vaa = gpd.read_file(gdb_path, layer='NHDPlusFlowlineVAA')
    logger.info(f"Loaded {len(flowline_vaa)} flowline VAA records")
    
    # Merge flowlines with VAA attributes
    flowlines_with_vaa = all_flowlines.merge(
        flowline_vaa[['nhdplusid', 'hydroseq', 'dnhydroseq', 'streamorde', 'streamcalc', 'totdasqkm', 'areasqkm']],
        left_on='nhdplusid_',
        right_on='nhdplusid',
        how='inner'
    )
    logger.info(f"Merged to {len(flowlines_with_vaa)} flowlines with VAA attributes")
    
    # Convert to UTM for distance calculations
    flowlines_utm = flowlines_with_vaa.to_crs("EPSG:26912")
    
    # Step 1: Find flowline closest to dam
    logger.info("Finding flowline closest to dam...")
    distances = flowlines_utm.geometry.distance(dam_point)
    closest_idx = distances.idxmin()
    closest_flowline = flowlines_utm.loc[closest_idx]
    closest_distance = distances.iloc[closest_idx]
    
    logger.info(f"Closest flowline to dam: nhdplusid {closest_flowline['nhdplusid']}")
    logger.info(f"Distance to dam: {closest_distance:.2f} meters")
    logger.info(f"Stream order: {closest_flowline['streamorde']}")
    logger.info(f"Hydroseq: {closest_flowline['hydroseq']}")
    
    # Step 2: Enhanced downstream tracing using hydroseq
    logger.info("Starting enhanced downstream tracing...")
    
    downstream_flowlines = []
    visited_hydroseqs = set()
    
    # Create lookup dictionaries for efficient searching
    hydroseq_to_flowline = {row['hydroseq']: row for _, row in flowlines_utm.iterrows()}
    
    # Add the starting flowline
    downstream_flowlines.append(closest_flowline)
    visited_hydroseqs.add(closest_flowline['hydroseq'])
    current_hydroseq = closest_flowline['dnhydroseq']
    
    # Strategy 1: Follow main downstream path using hydroseq
    logger.info("Strategy 1: Following main downstream path using hydroseq...")
    iteration = 0
    max_iterations = 200  # Increased limit
    
    while iteration < max_iterations and current_hydroseq != 0:
        iteration += 1
        
        # Skip if we've already visited this hydroseq
        if current_hydroseq in visited_hydroseqs:
            logger.info(f"Cycle detected at iteration {iteration} - stopping main path")
            break
            
        # Find flowline with this hydroseq
        if current_hydroseq in hydroseq_to_flowline:
            next_flowline = hydroseq_to_flowline[current_hydroseq]
            
            # Check distance from dam
            distance_from_dam = dam_point.distance(next_flowline.geometry)
            if distance_from_dam > max_distance_km * 1000:
                logger.info(f"Reached maximum distance ({distance_from_dam/1000:.1f}km) at iteration {iteration}")
                break
            
            # Check stream order criteria
            stream_order = next_flowline.get('streamorde', 0)
            if stream_order < min_stream_order or stream_order > max_stream_order:
                logger.info(f"Stream order {stream_order} outside range [{min_stream_order}, {max_stream_order}] at iteration {iteration}")
                # Continue to next flowline instead of breaking
                current_hydroseq = next_flowline['dnhydroseq']
                continue
            
            downstream_flowlines.append(next_flowline)
            visited_hydroseqs.add(current_hydroseq)
            current_hydroseq = next_flowline['dnhydroseq']
            
            logger.info(f"Added downstream flowline: hydroseq {next_flowline['hydroseq']}, "
                       f"stream order {stream_order}, distance {distance_from_dam/1000:.1f}km")
        else:
            logger.info(f"No flowline found for hydroseq {current_hydroseq} at iteration {iteration}")
            break
    
    if current_hydroseq == 0:
        logger.info(f"Reached network terminus (hydroseq = 0) at iteration {iteration}")
    
    logger.info(f"Main path found {len(downstream_flowlines)} flowlines")
    
    # Strategy 2: Add major tributaries using stream order
    logger.info("Strategy 2: Adding major tributaries...")
    
    # For each flowline in main path, find tributaries that join it
    main_hydroseqs = [fl['hydroseq'] for fl in downstream_flowlines]
    
    for main_hydroseq in main_hydroseqs:
        # Find flowlines that flow into this main stem flowline (dnhydroseq = main_hydroseq)
        tributaries = flowlines_utm[
            (flowlines_utm['dnhydroseq'] == main_hydroseq) & 
            (~flowlines_utm['hydroseq'].isin(visited_hydroseqs))
        ]
        
        for _, trib in tributaries.iterrows():
            # Only add significant tributaries
            stream_order = trib.get('streamorde', 0)
            drainage_area = trib.get('totdasqkm', 0)
            
            if (stream_order >= min_stream_order and 
                stream_order <= max_stream_order and 
                drainage_area > 10):  # At least 10 sq km drainage area
                
                distance_from_dam = dam_point.distance(trib.geometry)
                if distance_from_dam <= max_distance_km * 1000:
                    downstream_flowlines.append(trib)
                    visited_hydroseqs.add(trib['hydroseq'])
                    logger.info(f"Added tributary: hydroseq {trib['hydroseq']}, "
                               f"stream order {stream_order}, drainage area {drainage_area:.1f} sq km")
    
    logger.info(f"Total flowlines after adding tributaries: {len(downstream_flowlines)}")
    
    # Step 3: Create final GeoDataFrame and sort by distance from dam
    logger.info("Creating final downstream flowlines dataset...")
    
    downstream_gdf = gpd.GeoDataFrame(downstream_flowlines)
    
    # Calculate distance from dam for sorting
    distances = []
    for _, flowline in downstream_gdf.iterrows():
        distance = dam_point.distance(flowline.geometry)
        distances.append(distance)
    
    downstream_gdf['dam_distance'] = distances
    downstream_gdf = downstream_gdf.sort_values('dam_distance')
    
    # Add LINKNO field (1-based, closest to dam = 1)
    downstream_gdf['LINKNO'] = range(1, len(downstream_gdf) + 1)
    
    logger.info(f"Closest flowline: {downstream_gdf.iloc[0]['dam_distance']:.1f}m from dam")
    logger.info(f"Farthest flowline: {downstream_gdf.iloc[-1]['dam_distance']:.1f}m from dam")
    
    # Step 4: Save the improved downstream flowlines
    improved_file = output_dir / "improved_downstream_flowlines.shp"
    downstream_gdf.to_file(improved_file)
    logger.info(f"Saved {len(downstream_gdf)} improved downstream flowlines to {improved_file}")
    
    # Step 5: Create raster from improved flowlines
    logger.info("Creating improved downstream stream network raster...")
    
    stream_raster_file = output_dir / "improved_stream_network.tif"
    
    rasterize_cmd = [
        'gdal_rasterize',
        '-a', 'LINKNO',
        '-tr', '30', '30',
        '-a_nodata', '-9999',
        '-te', '325034.9880469', '4494308.282', '451934.988', '4620210.564',
        '-ot', 'Int32',
        str(improved_file),
        str(stream_raster_file)
    ]
    
    result = subprocess.run(rasterize_cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        logger.error(f"Rasterize failed: {result.stderr}")
        return False
    
    logger.info("✅ Improved stream network raster created")
    
    # Step 6: Copy to main output files
    logger.info("Copying to main output files...")
    
    import shutil
    
    # Copy raster
    shutil.copy2(stream_raster_file, output_dir / "stream_network.tif")
    
    # Copy vector files
    for ext in ['.shp', '.shx', '.dbf', '.prj', '.cpg']:
        src_file = improved_file.with_suffix(ext)
        dst_file = (output_dir / "stream_network").with_suffix(ext)
        if src_file.exists():
            shutil.copy2(src_file, dst_file)
    
    logger.info("✅ Copied improved stream network files")
    
    # Step 7: Create detailed summary report
    total_length = downstream_gdf['lengthkm'].sum()
    avg_distance = downstream_gdf['dam_distance'].mean() / 1000
    max_stream_order = downstream_gdf['streamorde'].max()
    min_stream_order = downstream_gdf['streamorde'].min()
    
    report = f"""
# Improved Downstream Stream Network Summary

## Network Statistics:
- **Total flowlines**: {len(downstream_gdf)}
- **Total length**: {total_length:.2f} km
- **Average distance from dam**: {avg_distance:.2f} km
- **Distance range**: {downstream_gdf.iloc[0]['dam_distance']:.1f}m to {downstream_gdf.iloc[-1]['dam_distance']:.1f}m
- **Stream order range**: {min_stream_order} to {max_stream_order}

## Enhanced Tracing Strategy:
- **Main path**: Using NHDPlusFlowlineVAA hydroseq for accurate connectivity
- **Tributaries**: Major tributaries with drainage area > 10 sq km
- **Stream order filter**: Orders {min_stream_order} to {max_stream_order}
- **Distance limit**: {max_distance_km} km from dam

## Stream Order Distribution:
"""
    
    # Add stream order distribution
    order_counts = downstream_gdf['streamorde'].value_counts().sort_index()
    for order, count in order_counts.items():
        report += f"- **Order {int(order)}**: {count} flowlines\n"
    
    report += f"""
## Comparison with Previous Results:
- **Basic downstream tracing**: 15 flowlines
- **Extended downstream tracing**: 26 flowlines  
- **Improved downstream tracing**: {len(downstream_gdf)} flowlines
- **Reference net2.shp**: 7 flowlines

## Key Improvements:
- ✅ Uses NHDPlusFlowlineVAA for accurate connectivity
- ✅ Configurable stream order and distance criteria
- ✅ Includes major tributaries based on drainage area
- ✅ More comprehensive downstream coverage
- ✅ Better attribute preservation

This improved network provides the most comprehensive and accurate representation
of downstream flow paths from Mountain Dell Dam using enhanced NHD Plus connectivity.
"""
    
    report_file = output_dir / "improved_network_report.md"
    with open(report_file, 'w') as f:
        f.write(report)
    
    logger.info(f"✅ Created improved network report: {report_file}")
    
    return True

if __name__ == "__main__":
    logger.info("Starting improved downstream path generation...")
    
    # You can adjust these parameters:
    # - max_stream_order: Higher = include larger streams (1-8 typical range)
    # - min_stream_order: Lower = include smaller streams  
    # - max_distance_km: How far downstream to trace
    
    success = generate_improved_downstream_path(
        max_stream_order=8,    # Include all stream orders
        min_stream_order=1,    # Include even small streams
        max_distance_km=300    # Trace up to 300km downstream
    )
    
    if success:
        logger.info("Improved downstream path generation completed successfully!")
    else:
        logger.error("Improved downstream path generation failed!")
