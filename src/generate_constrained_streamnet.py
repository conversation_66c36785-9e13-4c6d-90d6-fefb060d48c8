#!/usr/bin/env python3
"""
Generate constrained stream network using outlet points from downstream flowlines
"""

import subprocess
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_constrained_streamnet():
    """Generate stream network constrained by outlet points"""
    
    output_dir = Path("outputs")
    
    # Input files
    flow_dir_file = output_dir / "flow_directions.tif"
    dem_filled_file = output_dir / "dem_filled.tif"
    flow_acc_file = output_dir / "flow_accumulation.tif"
    outlet_points_file = output_dir / "outlet_points.shp"
    
    # Output files
    stream_raster_file = output_dir / "constrained_stream_network.tif"
    stream_vector_file = output_dir / "constrained_stream_network.shp"
    stream_order_file = output_dir / "constrained_stream_order.tif"
    watershed_file = output_dir / "constrained_watersheds.tif"
    tree_file = output_dir / "constrained_tree.txt"
    coord_file = output_dir / "constrained_coordinates.txt"
    
    # Step 1: First create a stream source raster using threshold
    logger.info("Creating stream source raster...")

    stream_source_file = output_dir / "stream_source.tif"

    threshold_cmd = [
        'mpiexec', '-n', '1', 'threshold',
        '-ssa', str(flow_acc_file),
        '-src', str(stream_source_file),
        '-thresh', '0.01'  # Use a reasonable threshold
    ]

    logger.info(f"Running threshold command: {' '.join(threshold_cmd)}")
    result = subprocess.run(threshold_cmd, capture_output=True, text=True)

    if result.returncode != 0:
        logger.error(f"Threshold failed: {result.stderr}")
        return False

    logger.info("✅ Stream source raster created")

    # Step 2: Use TauDEM streamnet with outlet points to generate constrained stream network
    logger.info("Running TauDEM streamnet with outlet points...")

    streamnet_cmd = [
        'mpiexec', '-n', '8', 'streamnet',
        '-p', str(flow_dir_file),
        '-fel', str(dem_filled_file),
        '-ad8', str(flow_acc_file),
        '-src', str(stream_source_file),  # Input stream source raster
        '-o', str(outlet_points_file),    # Outlet points constraint
        '-ord', str(stream_order_file),
        '-tree', str(tree_file),
        '-coord', str(coord_file),
        '-net', str(stream_vector_file),
        '-w', str(watershed_file)
    ]

    logger.info(f"Running streamnet command: {' '.join(streamnet_cmd)}")
    result = subprocess.run(streamnet_cmd, capture_output=True, text=True)

    if result.returncode != 0:
        logger.error(f"Streamnet failed: {result.stderr}")
        return False
    
    logger.info("✅ Constrained stream network generated successfully!")
    
    # Step 2: Check the results
    logger.info("Checking generated files...")
    
    for file_path in [stream_vector_file, stream_order_file, watershed_file]:
        if file_path.exists():
            logger.info(f"✅ Generated: {file_path}")
        else:
            logger.warning(f"❌ Missing: {file_path}")
    
    # Step 3: Get statistics on the stream network
    if stream_vector_file.exists():
        check_cmd = ['ogrinfo', str(stream_vector_file), '-al', '-so']
        result = subprocess.run(check_cmd, capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Feature Count:' in line:
                    logger.info(f"Stream network features: {line.strip()}")
                    break
    
    # Check if we can create a stream raster from the vector
    if stream_vector_file.exists():
        logger.info("Stream vector file exists - we can create a raster from it if needed")
    
    # Step 4: Copy to main output files
    logger.info("Copying to main output files...")
    
    import shutil
    
    # Create stream network raster from vector if needed
    if stream_vector_file.exists():
        logger.info("Creating stream network raster from vector...")

        # Use gdal_rasterize to create raster from vector
        rasterize_cmd = [
            'gdal_rasterize',
            '-a', 'LINKNO',  # Use LINKNO attribute for raster values
            '-tr', '30', '30',  # 30m resolution
            '-a_nodata', '-9999',
            '-te', '325034.9880469', '4494308.282', '451934.988', '4620210.564',  # Use study area extent
            '-ot', 'Int32',
            str(stream_vector_file),
            str(output_dir / "stream_network.tif")
        ]

        result = subprocess.run(rasterize_cmd, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✅ Created stream network raster from vector")
        else:
            logger.warning(f"Failed to create stream raster: {result.stderr}")
    
    if stream_order_file.exists():
        shutil.copy2(stream_order_file, output_dir / "stream_order.tif")
        logger.info("✅ Copied stream order raster")
    
    if watershed_file.exists():
        shutil.copy2(watershed_file, output_dir / "watersheds.tif")
        logger.info("✅ Copied watersheds raster")
    
    # Copy vector files (all components)
    if stream_vector_file.exists():
        for ext in ['.shp', '.shx', '.dbf', '.prj']:
            src_file = stream_vector_file.with_suffix(ext)
            dst_file = (output_dir / "stream_network").with_suffix(ext)
            if src_file.exists():
                shutil.copy2(src_file, dst_file)
        logger.info("✅ Copied stream network vector")
    
    # Copy text files
    if tree_file.exists():
        shutil.copy2(tree_file, output_dir / "tree.txt")
        logger.info("✅ Copied tree file")
    
    if coord_file.exists():
        shutil.copy2(coord_file, output_dir / "coordinates.txt")
        logger.info("✅ Copied coordinates file")
    
    return True

if __name__ == "__main__":
    logger.info("Starting constrained stream network generation...")
    success = generate_constrained_streamnet()
    if success:
        logger.info("Constrained stream network generation completed successfully!")
    else:
        logger.error("Constrained stream network generation failed!")
