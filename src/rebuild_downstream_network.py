#!/usr/bin/env python3
"""
Rebuild downstream HUC12 network to include all tributaries, not just main stem
"""

import geopandas as gpd
import logging
from pathlib import Path
import numpy as np

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def rebuild_downstream_network(max_distance_km=100):
    """Rebuild downstream HUC12 network including all tributaries."""
    
    output_dir = Path("outputs")
    gdb_path = "data/NHDPLUS_H_1602_HU4_20220412_GDB/NHDPLUS_H_1602_HU4_20220412_GDB.gdb"
    
    # Load all HUC12s
    logger.info("Loading all HUC12s...")
    all_hucs = gpd.read_file(gdb_path, layer='WBDHU12')
    logger.info(f"Loaded {len(all_hucs)} HUC12s")
    
    # Load the dam HUC12
    dam_huc12_gdf = gpd.read_file(output_dir / "dam_huc12.shp")
    dam_huc12_code = dam_huc12_gdf.iloc[0]['huc12']
    logger.info(f"Dam HUC12: {dam_huc12_code}")
    
    # Step 1: Build the main stem downstream path (as before)
    logger.info("Building main stem downstream path...")
    huc_dict = {row['huc12']: row for _, row in all_hucs.iterrows()}
    
    main_stem_hucs = []
    current_huc = dam_huc12_code
    visited_main = set()
    cumulative_distance = 0
    
    while current_huc and current_huc not in visited_main and cumulative_distance < max_distance_km * 1000:
        visited_main.add(current_huc)
        
        if current_huc in huc_dict:
            huc_row = huc_dict[current_huc]
            main_stem_hucs.append(huc_row)
            
            # Calculate approximate distance
            area_km2 = huc_row['areasqkm']
            estimated_length = np.sqrt(area_km2) * 1000
            cumulative_distance += estimated_length
            
            logger.info(f"Main stem: {current_huc} - {huc_row['name']} "
                      f"(cumulative: {cumulative_distance/1000:.1f}km)")
            
            # Get next downstream HUC
            tohuc = huc_row.get('tohuc', '')
            if tohuc and len(tohuc) >= 12 and tohuc != 'CLOSED BASIN':
                current_huc = tohuc[:12]
            else:
                break
        else:
            break
    
    main_stem_codes = [huc['huc12'] for huc in main_stem_hucs]
    logger.info(f"Main stem has {len(main_stem_codes)} HUC12s: {main_stem_codes}")
    
    # Step 2: Find all tributaries that drain to the main stem HUC12s
    logger.info("Finding all tributaries to main stem...")
    
    all_downstream_hucs = main_stem_hucs.copy()
    tributary_codes = set()
    
    # For each main stem HUC12, find all HUC12s that drain to it
    for main_huc_code in main_stem_codes:
        tributaries = all_hucs[all_hucs['tohuc'] == main_huc_code]
        
        for _, trib in tributaries.iterrows():
            trib_code = trib['huc12']
            if trib_code not in main_stem_codes and trib_code not in tributary_codes:
                tributary_codes.add(trib_code)
                all_downstream_hucs.append(trib)
                logger.info(f"Added tributary: {trib_code} - {trib['name']} -> {main_huc_code}")
    
    # Step 3: Recursively find tributaries to tributaries (up to 2 levels)
    logger.info("Finding tributaries to tributaries...")
    
    level2_tributary_codes = set()
    for trib_code in tributary_codes:
        level2_tributaries = all_hucs[all_hucs['tohuc'] == trib_code]
        
        for _, trib2 in level2_tributaries.iterrows():
            trib2_code = trib2['huc12']
            if (trib2_code not in main_stem_codes and 
                trib2_code not in tributary_codes and 
                trib2_code not in level2_tributary_codes):
                level2_tributary_codes.add(trib2_code)
                all_downstream_hucs.append(trib2)
                logger.info(f"Added level-2 tributary: {trib2_code} - {trib2['name']} -> {trib_code}")
    
    # Step 4: Filter by distance constraint
    logger.info("Applying distance constraint...")
    
    # Load dam location for distance calculation
    dam_location = gpd.read_file("data/mountain_dell_dam_location.shp")
    dam_point = dam_location.to_crs(all_hucs.crs).geometry.iloc[0]
    
    filtered_hucs = []
    for huc in all_downstream_hucs:
        # Calculate distance from dam to HUC centroid
        huc_centroid = huc['geometry'].centroid
        distance_m = dam_point.distance(huc_centroid)
        distance_km = distance_m / 1000
        
        if distance_km <= max_distance_km:
            filtered_hucs.append(huc)
            logger.info(f"Included: {huc['huc12']} - {huc['name']} ({distance_km:.1f}km from dam)")
        else:
            logger.info(f"Excluded (too far): {huc['huc12']} - {huc['name']} ({distance_km:.1f}km from dam)")
    
    # Step 5: Create final GeoDataFrame and save
    logger.info("Creating final downstream HUC12 network...")
    
    if filtered_hucs:
        downstream_hucs_gdf = gpd.GeoDataFrame(filtered_hucs)
        downstream_hucs_gdf.crs = all_hucs.crs
        
        # Save the expanded downstream HUCs
        downstream_file = output_dir / "downstream_huc12s_expanded.shp"
        downstream_hucs_gdf.to_file(downstream_file)
        
        logger.info(f"✅ Expanded downstream network: {len(downstream_hucs_gdf)} HUC12s")
        logger.info(f"✅ Saved to: {downstream_file}")
        
        # Also update the main downstream file
        downstream_hucs_gdf.to_file(output_dir / "downstream_huc12s.shp")
        logger.info(f"✅ Updated main downstream HUC12s file")
        
        # Create summary
        summary = f"""
# Expanded Downstream HUC12 Network Summary

## Network Statistics:
- **Total HUC12s**: {len(downstream_hucs_gdf)}
- **Main stem HUC12s**: {len(main_stem_codes)}
- **Level-1 tributaries**: {len(tributary_codes)}
- **Level-2 tributaries**: {len(level2_tributary_codes)}
- **Distance constraint**: {max_distance_km} km from dam

## Main Stem Path:
"""
        for i, huc_code in enumerate(main_stem_codes):
            huc_name = next(h['name'] for h in main_stem_hucs if h['huc12'] == huc_code)
            summary += f"{i+1}. {huc_code}: {huc_name}\n"
        
        summary += f"\n## All HUC12s in Network:\n"
        for _, huc in downstream_hucs_gdf.iterrows():
            summary += f"- {huc['huc12']}: {huc['name']}\n"
        
        report_file = output_dir / "expanded_downstream_network_report.md"
        with open(report_file, 'w') as f:
            f.write(summary)
        
        logger.info(f"✅ Created summary report: {report_file}")
        
        return True
    else:
        logger.error("No HUC12s found in expanded network")
        return False

if __name__ == "__main__":
    logger.info("Starting expanded downstream network rebuild...")
    success = rebuild_downstream_network()
    if success:
        logger.info("Expanded downstream network rebuild completed successfully!")
    else:
        logger.error("Expanded downstream network rebuild failed!")
