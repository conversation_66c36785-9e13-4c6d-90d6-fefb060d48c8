#!/usr/bin/env python3
"""
Generate downstream flowlines from dam location using the correct algorithm
"""

import subprocess
import logging
import geopandas as gpd
import numpy as np
from pathlib import Path
from shapely.geometry import Point
import pandas as pd

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_downstream_flowlines():
    """Generate flowlines downstream from dam using the correct algorithm"""
    
    output_dir = Path("outputs")
    
    # Load the dam location
    logger.info("Loading dam location...")
    dam_location = gpd.read_file("data/mountain_dell_dam_location.shp")
    dam_point = dam_location.geometry.iloc[0]
    logger.info(f"Dam location: {dam_point}")
    
    # Load the existing flowlines that we found in the downstream HUC12s
    logger.info("Loading target flowlines...")
    target_flowlines = gpd.read_file(output_dir / "target_flowlines.shp")
    logger.info(f"Found {len(target_flowlines)} flowlines in downstream HUC12s")
    
    # Load the flowline endpoints
    logger.info("Loading flowline endpoints...")
    flowline_endpoints = gpd.read_file(output_dir / "flowline_endpoints.shp")
    logger.info(f"Found {len(flowline_endpoints)} flowline endpoints")
    
    # Step 1: Find the flowline that contains or is closest to the dam
    logger.info("Finding flowline closest to dam...")
    
    # Reproject dam to match flowlines CRS
    dam_reproj = dam_location.to_crs(target_flowlines.crs)
    dam_point_reproj = dam_reproj.geometry.iloc[0]
    
    # Find the closest flowline to the dam
    distances = target_flowlines.geometry.distance(dam_point_reproj)
    closest_flowline_idx = distances.idxmin()
    closest_flowline = target_flowlines.iloc[closest_flowline_idx]
    
    logger.info(f"Closest flowline to dam: nhdplusid {closest_flowline.get('nhdplusid_', 'N/A')}")
    logger.info(f"Distance to dam: {distances.iloc[closest_flowline_idx]:.2f} meters")
    
    # Step 2: Load NHD Plus flow connectivity data
    logger.info("Loading NHD Plus flow connectivity...")
    gdb_path = "data/NHDPLUS_H_1602_HU4_20220412_GDB/NHDPLUS_H_1602_HU4_20220412_GDB.gdb"
    flow_data = gpd.read_file(gdb_path, layer='NHDPlusFlow')
    logger.info(f"Loaded {len(flow_data)} flow connections")

    # Step 3: Trace downstream flowlines using NHD connectivity
    logger.info("Tracing downstream flowlines...")

    downstream_flowlines = []
    current_nhdplusid = closest_flowline.get('nhdplusid_')

    if current_nhdplusid is None:
        logger.error("No nhdplusid_ found for closest flowline")
        return False

    # Start with the closest flowline
    downstream_flowlines.append(closest_flowline)
    visited_ids = {current_nhdplusid}

    logger.info(f"Starting from flowline with nhdplusid: {current_nhdplusid}")

    # Trace downstream using flow connectivity
    max_iterations = 100  # Prevent infinite loops
    iteration = 0

    while iteration < max_iterations:
        iteration += 1

        # Find the next downstream flowline using flow connectivity
        downstream_flows = flow_data[flow_data['fromnhdpid'] == current_nhdplusid]

        if len(downstream_flows) == 0:
            logger.info(f"Reached end of network at iteration {iteration} - no downstream connections")
            break

        # Get the downstream nhdplusid
        next_nhdplusid = downstream_flows.iloc[0]['tonhdpid']

        if next_nhdplusid in visited_ids:
            logger.warning(f"Circular reference detected at nhdplusid {next_nhdplusid}")
            break

        # Find flowline with this nhdplusid
        next_flowlines = target_flowlines[target_flowlines['nhdplusid_'] == next_nhdplusid]

        if len(next_flowlines) == 0:
            logger.info(f"No flowline found for downstream nhdplusid {next_nhdplusid}")
            break

        next_flowline = next_flowlines.iloc[0]
        downstream_flowlines.append(next_flowline)
        visited_ids.add(next_nhdplusid)

        # Update for next iteration
        current_nhdplusid = next_nhdplusid

        logger.info(f"Added downstream flowline: nhdplusid {current_nhdplusid}")

    logger.info(f"Found {len(downstream_flowlines)} downstream flowlines")
    
    # Step 3: Create GeoDataFrame of downstream flowlines
    if len(downstream_flowlines) == 0:
        logger.error("No downstream flowlines found")
        return False
        
    downstream_gdf = gpd.GeoDataFrame(downstream_flowlines, crs=target_flowlines.crs)
    
    # Save downstream flowlines
    downstream_file = output_dir / "downstream_flowlines.shp"
    downstream_gdf.to_file(downstream_file)
    logger.info(f"Saved {len(downstream_gdf)} downstream flowlines to {downstream_file}")
    
    # Step 4: Get endpoints of downstream flowlines for streamnet
    logger.info("Extracting downstream flowline endpoints...")
    
    downstream_endpoints = []
    for idx, flowline in downstream_gdf.iterrows():
        if flowline.geometry.geom_type == 'LineString':
            coords = list(flowline.geometry.coords)
            if len(coords) > 0:
                # Get the downstream end (last point)
                endpoint = Point(coords[-1])
                downstream_endpoints.append({
                    'nhdplusid': flowline.get('nhdplusid_'),
                    'geometry': endpoint
                })
        elif flowline.geometry.geom_type == 'MultiLineString':
            for line in flowline.geometry.geoms:
                coords = list(line.coords)
                if len(coords) > 0:
                    endpoint = Point(coords[-1])
                    downstream_endpoints.append({
                        'nhdplusid': flowline.get('nhdplusid_'),
                        'geometry': endpoint
                    })
    
    if len(downstream_endpoints) == 0:
        logger.error("No downstream endpoints found")
        return False
        
    downstream_endpoints_gdf = gpd.GeoDataFrame(downstream_endpoints, crs=target_flowlines.crs)
    
    # Save downstream endpoints
    endpoints_file = output_dir / "downstream_endpoints.shp"
    downstream_endpoints_gdf.to_file(endpoints_file)
    logger.info(f"Saved {len(downstream_endpoints_gdf)} downstream endpoints to {endpoints_file}")
    
    # Step 5: Use these endpoints as outlet points for TauDEM streamnet
    logger.info("Using downstream endpoints as outlet points for TauDEM...")
    
    # Reproject endpoints to match DEM CRS (UTM 12N)
    endpoints_utm = downstream_endpoints_gdf.to_crs("EPSG:26912")
    
    # Create outlet points file for TauDEM
    outlet_file = output_dir / "outlet_points.shp"
    endpoints_utm.to_file(outlet_file)
    logger.info(f"Created outlet points file: {outlet_file}")
    
    return True

if __name__ == "__main__":
    logger.info("Starting downstream flowline generation...")
    success = generate_downstream_flowlines()
    if success:
        logger.info("Downstream flowline generation completed successfully!")
    else:
        logger.error("Downstream flowline generation failed!")
