#!/usr/bin/env python3
"""
Regenerate stream network with appropriate threshold values
"""

import subprocess
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def regenerate_stream_network():
    """Regenerate stream network with multiple threshold values to find the best one"""
    
    output_dir = Path("outputs")
    flow_acc_file = output_dir / "flow_accumulation.tif"
    flow_dir_file = output_dir / "flow_directions.tif"
    dem_filled_file = output_dir / "dem_filled.tif"
    
    # Test different threshold values based on the flow accumulation statistics
    # Max value is 0.019, so let's try much smaller thresholds
    thresholds = [0.001, 0.002, 0.005, 0.01, 0.015]
    
    for i, thresh in enumerate(thresholds):
        logger.info(f"Testing threshold: {thresh}")
        
        # Create output files for this threshold
        stream_raster_file = output_dir / f"stream_network_thresh_{thresh}.tif"
        stream_vector_file = output_dir / f"stream_network_thresh_{thresh}.shp"
        stream_order_file = output_dir / f"stream_order_thresh_{thresh}.tif"
        watershed_file = output_dir / f"watersheds_thresh_{thresh}.tif"
        tree_file = output_dir / f"tree_thresh_{thresh}.txt"
        coord_file = output_dir / f"coordinates_thresh_{thresh}.txt"
        
        # Step 1: Create stream raster using threshold
        threshold_cmd = [
            'mpiexec', '-n', '1', 'threshold',
            '-ssa', str(flow_acc_file),
            '-src', str(stream_raster_file),
            '-thresh', str(thresh)
        ]
        
        logger.info(f"Running threshold command: {' '.join(threshold_cmd)}")
        result = subprocess.run(threshold_cmd, capture_output=True, text=True)
        if result.returncode != 0:
            logger.error(f"Threshold failed for {thresh}: {result.stderr}")
            continue
            
        # Check if stream raster has any streams
        check_cmd = ['gdalinfo', '-stats', str(stream_raster_file)]
        result = subprocess.run(check_cmd, capture_output=True, text=True)
        if "Maximum=0" in result.stdout:
            logger.warning(f"No streams found with threshold {thresh}")
            continue
        else:
            logger.info(f"✅ Streams found with threshold {thresh}!")
            
        # Step 2: Generate stream network using streamnet
        streamnet_cmd = [
            'mpiexec', '-n', '1', 'streamnet',
            '-p', str(flow_dir_file),
            '-fel', str(dem_filled_file),
            '-ad8', str(flow_acc_file),
            '-src', str(stream_raster_file),
            '-ord', str(stream_order_file),
            '-tree', str(tree_file),
            '-coord', str(coord_file),
            '-net', str(stream_vector_file),
            '-w', str(watershed_file)
        ]
        
        logger.info(f"Running streamnet command: {' '.join(streamnet_cmd)}")
        result = subprocess.run(streamnet_cmd, capture_output=True, text=True)
        if result.returncode != 0:
            logger.error(f"Streamnet failed for {thresh}: {result.stderr}")
            continue
            
        logger.info(f"✅ Successfully generated stream network with threshold {thresh}")
        
        # Copy the best result to the main output files
        if thresh == 0.005:  # Use 0.005 as the default good threshold
            import shutil
            
            # Copy raster files
            shutil.copy2(stream_raster_file, output_dir / "stream_network.tif")
            shutil.copy2(stream_order_file, output_dir / "stream_order.tif")
            shutil.copy2(watershed_file, output_dir / "watersheds.tif")
            
            # Copy vector files (all components)
            for ext in ['.shp', '.shx', '.dbf', '.prj']:
                src_file = stream_vector_file.with_suffix(ext)
                dst_file = (output_dir / "stream_network").with_suffix(ext)
                if src_file.exists():
                    shutil.copy2(src_file, dst_file)
            
            # Copy text files
            shutil.copy2(tree_file, output_dir / "tree.txt")
            shutil.copy2(coord_file, output_dir / "coordinates.txt")
            
            logger.info(f"✅ Copied threshold {thresh} results to main output files")

if __name__ == "__main__":
    logger.info("Starting stream network regeneration...")
    regenerate_stream_network()
    logger.info("Stream network regeneration completed!")
