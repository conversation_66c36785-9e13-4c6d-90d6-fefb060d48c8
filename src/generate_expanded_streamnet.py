#!/usr/bin/env python3
"""
Generate stream network using ALL flowlines in the expanded downstream HUC12 network
"""

import subprocess
import logging
import geopandas as gpd
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_expanded_streamnet():
    """Generate stream network using all flowlines in expanded HUC12 network"""
    
    output_dir = Path("outputs")
    
    # Load all flowlines from the expanded HUC12 network
    logger.info("Loading all flowlines from expanded HUC12 network...")
    all_flowlines = gpd.read_file(output_dir / "target_flowlines.shp")
    logger.info(f"Found {len(all_flowlines)} total flowlines in expanded network")
    
    # Load the dam location for reference
    dam_location = gpd.read_file("data/mountain_dell_dam_location.shp")
    dam_point = dam_location.to_crs(all_flowlines.crs).geometry.iloc[0]
    logger.info(f"Dam location: {dam_point}")
    
    # Step 1: Create a comprehensive stream network from ALL flowlines
    logger.info("Creating comprehensive stream network from all flowlines...")
    
    # Create simplified version with essential fields and unique IDs
    simplified_flowlines = all_flowlines[['permanent_', 'nhdplusid_', 'lengthkm', 'reachcode', 'geometry']].copy()
    simplified_flowlines['LINKNO'] = range(1, len(simplified_flowlines) + 1)
    
    # Calculate distance from dam to each flowline for prioritization
    distances = []
    for _, flowline in simplified_flowlines.iterrows():
        if flowline.geometry:
            # Get distance from dam to flowline
            distance = dam_point.distance(flowline.geometry)
            distances.append(distance)
        else:
            distances.append(float('inf'))
    
    simplified_flowlines['dam_distance'] = distances
    
    # Sort by distance from dam (closest first)
    simplified_flowlines = simplified_flowlines.sort_values('dam_distance')
    
    # Update LINKNO to reflect priority (closest to dam = lower numbers)
    simplified_flowlines['LINKNO'] = range(1, len(simplified_flowlines) + 1)
    
    logger.info(f"Closest flowline to dam: {simplified_flowlines.iloc[0]['dam_distance']:.1f}m")
    logger.info(f"Farthest flowline from dam: {simplified_flowlines.iloc[-1]['dam_distance']:.1f}m")
    
    # Save comprehensive flowlines
    comprehensive_file = output_dir / "comprehensive_flowlines.shp"
    simplified_flowlines.to_file(comprehensive_file)
    logger.info(f"Saved comprehensive flowlines to {comprehensive_file}")
    
    # Step 2: Create raster from ALL flowlines
    logger.info("Creating comprehensive stream network raster...")
    
    stream_raster_file = output_dir / "comprehensive_stream_network.tif"
    
    rasterize_cmd = [
        'gdal_rasterize',
        '-a', 'LINKNO',  # Use LINKNO attribute for raster values
        '-tr', '30', '30',  # 30m resolution
        '-a_nodata', '-9999',
        '-te', '325034.9880469', '4494308.282', '451934.988', '4620210.564',  # Study area extent
        '-ot', 'Int32',
        str(comprehensive_file),
        str(stream_raster_file)
    ]
    
    logger.info(f"Running rasterize command: {' '.join(rasterize_cmd)}")
    result = subprocess.run(rasterize_cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        logger.error(f"Rasterize failed: {result.stderr}")
        return False
    
    logger.info("✅ Comprehensive stream network raster created")
    
    # Step 3: Check the raster statistics
    logger.info("Checking raster statistics...")
    
    stats_cmd = ['gdalinfo', '-stats', str(stream_raster_file)]
    result = subprocess.run(stats_cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        lines = result.stdout.split('\n')
        for line in lines:
            if 'Minimum=' in line and 'Maximum=' in line:
                logger.info(f"Stream raster statistics: {line.strip()}")
                break
    
    # Step 4: Copy files to main output locations
    logger.info("Copying to main output files...")
    
    import shutil
    
    # Copy the stream raster
    shutil.copy2(stream_raster_file, output_dir / "stream_network.tif")
    logger.info("✅ Copied comprehensive stream network raster")
    
    # Copy the comprehensive vector files (all components)
    for ext in ['.shp', '.shx', '.dbf', '.prj']:
        src_file = comprehensive_file.with_suffix(ext)
        dst_file = (output_dir / "stream_network").with_suffix(ext)
        if src_file.exists():
            shutil.copy2(src_file, dst_file)
    logger.info("✅ Copied comprehensive stream network vector")
    
    # Step 5: Create a summary report
    logger.info("Creating comprehensive summary report...")
    
    # Calculate some statistics
    total_length_km = simplified_flowlines['lengthkm'].sum()
    avg_distance_km = simplified_flowlines['dam_distance'].mean() / 1000
    
    # Count flowlines by HUC12
    huc12_counts = {}
    for _, flowline in simplified_flowlines.iterrows():
        reachcode = flowline.get('reachcode', '')
        if reachcode and len(reachcode) >= 12:
            huc12 = reachcode[:12]
            huc12_counts[huc12] = huc12_counts.get(huc12, 0) + 1
    
    report = f"""
# Comprehensive Stream Network Summary

## Network Statistics:
- **Total flowlines**: {len(simplified_flowlines)}
- **Total length**: {total_length_km:.2f} km
- **Average distance from dam**: {avg_distance_km:.2f} km
- **HUC12s represented**: {len(huc12_counts)}
- **Stream network coverage**: Entire expanded downstream network (84 HUC12s)

## Distance Distribution:
- **Closest flowline**: {simplified_flowlines.iloc[0]['dam_distance']:.1f} m from dam
- **Farthest flowline**: {simplified_flowlines.iloc[-1]['dam_distance']:.1f} m from dam

## HUC12 Flowline Distribution:
"""
    
    # Add top 10 HUC12s by flowline count
    sorted_huc12s = sorted(huc12_counts.items(), key=lambda x: x[1], reverse=True)
    for i, (huc12, count) in enumerate(sorted_huc12s[:10]):
        report += f"{i+1}. {huc12}: {count} flowlines\n"
    
    if len(sorted_huc12s) > 10:
        report += f"... and {len(sorted_huc12s) - 10} more HUC12s\n"
    
    report += f"""
## Key Features:
- ✅ Includes ALL flowlines in expanded downstream network
- ✅ Prioritized by distance from dam (closer = lower LINKNO)
- ✅ Covers entire Great Salt Lake drainage basin downstream of dam
- ✅ Includes main stem + all tributaries + sub-tributaries
- ✅ Comprehensive representation for impact analysis

## Usage:
This comprehensive stream network represents ALL potential flow paths downstream 
from the Mountain Dell Dam and can be used for:
- Complete downstream impact assessment
- Comprehensive flow routing analysis
- Full watershed connectivity analysis
- Regional water quality modeling
- Complete flood routing studies
"""
    
    report_file = output_dir / "comprehensive_network_report.md"
    with open(report_file, 'w') as f:
        f.write(report)
    
    logger.info(f"✅ Created comprehensive summary report: {report_file}")
    
    return True

if __name__ == "__main__":
    logger.info("Starting comprehensive stream network generation...")
    success = generate_expanded_streamnet()
    if success:
        logger.info("Comprehensive stream network generation completed successfully!")
    else:
        logger.error("Comprehensive stream network generation failed!")
